# Artists Preview — 5‑Card Grid (5 artists) + Detail Page Template

Drop these into **/artists** (grid) and individual **/artists/\*\*\*\*.html** (detail pages). The grid uses inline SVG clip‑paths to get the rounded “blob” masks from your reference Pen and includes reduced‑motion and non‑clip‑path fallbacks. All images have explicit `alt`, lazy loading, and proper focus rings. Links open internal HTML pages.

---

## 1) `/artists` — 5‑Artist Preview Grid (HTML)

```html
<section class="artist-preview" aria-label="Featured artists — Dallas">
  <!-- SVG clip-path defs (once per page) -->
  <svg class="visually-hidden" aria-hidden="true" width="0" height="0" focusable="false">
    <defs>
      <clipPath id="blob-1" clipPathUnits="objectBoundingBox">
        <path d="M0.02,0.42 C0.05,0.22 0.20,0.06 0.40,0.06 L0.88,0.06 C0.96,0.06 0.99,0.16 0.98,0.30 C0.96,0.46 0.86,0.66 0.66,0.70 C0.49,0.74 0.40,0.67 0.27,0.72 C0.15,0.76 0.05,0.68 0.03,0.55 Z"/>
      </clipPath>
      <clipPath id="blob-2" clipPathUnits="objectBoundingBox">
        <path d="M0.03,0.38 C0.09,0.16 0.23,0.06 0.45,0.06 L0.86,0.06 C0.96,0.06 1.00,0.18 0.98,0.34 C0.96,0.50 0.87,0.69 0.68,0.73 C0.50,0.77 0.38,0.68 0.24,0.74 C0.12,0.78 0.01,0.70 0.02,0.55 Z"/>
      </clipPath>
      <clipPath id="blob-3" clipPathUnits="objectBoundingBox">
        <path d="M0.04,0.40 C0.07,0.20 0.22,0.07 0.42,0.07 L0.85,0.07 C0.96,0.07 1.00,0.20 0.98,0.35 C0.95,0.52 0.85,0.70 0.64,0.73 C0.44,0.77 0.34,0.67 0.20,0.73 C0.09,0.77 0.01,0.68 0.02,0.53 Z"/>
      </clipPath>
      <clipPath id="blob-4" clipPathUnits="objectBoundingBox">
        <path d="M0.02,0.36 C0.06,0.17 0.21,0.05 0.39,0.05 L0.86,0.05 C0.96,0.05 1.00,0.17 0.98,0.32 C0.95,0.49 0.85,0.69 0.65,0.73 C0.47,0.76 0.36,0.68 0.22,0.74 C0.10,0.78 0.02,0.70 0.02,0.56 Z"/>
      </clipPath>
      <clipPath id="blob-5" clipPathUnits="objectBoundingBox">
        <path d="M0.03,0.39 C0.08,0.18 0.23,0.06 0.41,0.06 L0.87,0.06 C0.97,0.06 1.00,0.17 0.98,0.33 C0.96,0.49 0.86,0.68 0.67,0.72 C0.49,0.76 0.39,0.68 0.26,0.73 C0.14,0.77 0.04,0.70 0.03,0.56 Z"/>
      </clipPath>
    </defs>
  </svg>

  <ul class="artist-grid" role="list">
    <li class="artist-card">
      <a class="artist-link" href="/artists/danica.html" aria-label="View Danica — bio and works">
        <figure class="media blob-1">
          <img src="/assets/artists/danica/thumb.jpg" alt="Danica — portrait" loading="lazy" width="640" height="420">
        </figure>
        <span class="name">Danica</span>
      </a>
    </li>

    <li class="artist-card">
      <a class="artist-link" href="/artists/david-v.html" aria-label="View David V — bio and works">
        <figure class="media blob-2">
          <img src="/assets/artists/david-v/thumb.jpg" alt="David V — portrait with hat" loading="lazy" width="640" height="420">
        </figure>
        <span class="name">David V.</span>
      </a>
    </li>

    <li class="artist-card">
      <a class="artist-link" href="/artists/omi.html" aria-label="View Omi — bio and works">
        <figure class="media blob-3">
          <img src="/assets/artists/omi/thumb.jpg" alt="Omi — profile with sunglasses" loading="lazy" width="640" height="420">
        </figure>
        <span class="name">Omi</span>
      </a>
    </li>

    <li class="artist-card">
      <a class="artist-link" href="/artists/sofia.html" aria-label="View Sofía — bio and works">
        <figure class="media blob-4">
          <img src="/assets/artists/sofia/thumb.jpg" alt="Sofía — portrait with hat" loading="lazy" width="640" height="420">
        </figure>
        <span class="name">Sofía</span>
      </a>
    </li>

    <li class="artist-card">
      <a class="artist-link" href="/artists/leon.html" aria-label="View León — bio and works">
        <figure class="media blob-5">
          <img src="/assets/artists/leon/thumb.jpg" alt="León — studio portrait" loading="lazy" width="640" height="420">
        </figure>
        <span class="name">León</span>
      </a>
    </li>
  </ul>
</section>
```

## 2) `/artists` — Grid Styles (CSS)

```css
/* Base */
.artist-preview{--card-bg:#101113;--card-bd:#ffffff1a;--name:#f2f2f2}
.artist-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(220px,1fr));gap:28px;margin:0;padding:0;list-style:none}

/* Card */
.artist-link{display:grid;gap:.55rem;text-decoration:none;color:var(--name);outline:0}
.artist-link:focus-visible{box-shadow:0 0 0 3px #fff3; border-radius:14px}
.media{background:linear-gradient(#15161a,#15161a) padding-box;border:1px solid var(--card-bd);border-radius:16px;overflow:hidden;aspect-ratio: 16 / 10}
.media img{width:100%;height:100%;object-fit:cover;display:block;filter:saturate(.98)}

/* Organic masks mapped to the SVG clipPaths */
.blob-1{clip-path:url(#blob-1)}
.blob-2{clip-path:url(#blob-2)}
.blob-3{clip-path:url(#blob-3)}
.blob-4{clip-path:url(#blob-4)}
.blob-5{clip-path:url(#blob-5)}

/* Motion & hover */
.artist-card{transition:transform .4s cubic-bezier(.2,.8,.2,1)}
.artist-card:hover{transform:translateY(-4px)}
.name{font-weight:800;letter-spacing:.02em;font-size:1rem}

/* Fallback when clip-path unsupported */
@supports not (clip-path: url(#blob-1)){
  .media{clip-path:none;border-radius:18px}
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce){
  .artist-card{transition:none}
}

/* Mobile density */
@media (max-width:480px){
  .artist-grid{gap:18px}
}

/* Visually hidden utility */
.visually-hidden{position:absolute !important;clip:rect(0 0 0 0);clip-path:inset(50%);width:1px;height:1px;overflow:hidden;white-space:nowrap}
```

---

## 3) Artist Detail Page Template (`/artists/<slug>.html`)

> One‑page bio + 3–5 artwork carousel. Pure HTML/CSS with scroll‑snap; works without JS and respects reduced motion. Duplicate this file per artist and swap content/paths.

```html
<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Danica — Chocolate & Art Show Dallas</title>
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800&display=swap" rel="stylesheet">
<style>
  :root{--bg:#0b0b0d;--fg:#ececec;--muted:#b6beca}
  html,body{height:100%}
  body{margin:0;background:var(--bg);color:var(--fg);font:400 16px/1.55 Inter,system-ui,Segoe UI,Roboto,Arial}
  .wrap{max-width:1100px;margin:0 auto;padding:26px}
  a{color:#a0c2ff}

  /* Header with blob image (reusing defs) */
  header{display:grid;grid-template-columns: 340px 1fr;gap:20px;align-items:end}
  .portrait{background:#15161a;border:1px solid #ffffff1a;border-radius:16px;overflow:hidden;aspect-ratio: 16/10;clip-path:url(#blob-2)}
  .portrait img{width:100%;height:100%;object-fit:cover;display:block}
  h1{margin:0 0 .2rem 0;font-size:clamp(1.8rem,4.5vw,3rem);font-weight:800}
  .meta{color:var(--muted);font-weight:600}

  /* Bio */
  .bio{margin:18px 0 28px;max-width:70ch}

  /* Carousel (scroll-snap) */
  .carousel{display:flex;gap:12px;overflow-x:auto;scroll-snap-type:x mandatory;padding-bottom:8px}
  .slide{flex:0 0 82%;scroll-snap-align:center;background:#111319;border:1px solid #ffffff1a;border-radius:14px;overflow:hidden}
  .slide img{display:block;width:100%;height:auto}
  figcaption{padding:.65rem .8rem;font-size:.95rem;color:var(--muted)}

  /* Nav buttons (progressive enhancement) */
  .nav{display:flex;gap:10px;margin:10px 0}
  .nav button{appearance:none;background:#ffffff14;border:1px solid #ffffff33;color:#fff;border-radius:10px;padding:.6rem .9rem;font-weight:700}
  .nav button:focus-visible{outline:3px solid #fff3}

  /* Back link */
  .back{display:inline-block;margin:20px 0 0;font-weight:700}

  @media (max-width:760px){header{grid-template-columns:1fr}}
  @supports not (clip-path: url(#blob-2)){.portrait{clip-path:none;border-radius:18px}}
</style>
</head>
<body>
<div class="wrap">
  <!-- SVG defs (can be copied once per site into a shared partial) -->
  <svg class="visually-hidden" aria-hidden="true" width="0" height="0" focusable="false">
    <defs>
      <clipPath id="blob-2" clipPathUnits="objectBoundingBox">
        <path d="M0.03,0.38 C0.09,0.16 0.23,0.06 0.45,0.06 L0.86,0.06 C0.96,0.06 1.00,0.18 0.98,0.34 C0.96,0.50 0.87,0.69 0.68,0.73 C0.50,0.77 0.38,0.68 0.24,0.74 C0.12,0.78 0.01,0.70 0.02,0.55 Z"/>
      </clipPath>
    </defs>
  </svg>

  <a class="back" href="/artists">← Back to Artists</a>

  <header>
    <figure class="portrait">
      <img src="/assets/artists/danica/thumb.jpg" alt="Danica — portrait" width="1200" height="750">
    </figure>
    <div>
      <h1>Danica</h1>
      <div class="meta">Mixed Media • Dallas • @danica.art</div>
      <p class="bio">Short bio goes here. Two or three sentences on medium, influences, and what they’re bringing to Chocolate & Art Dallas. Keep it tight for mobile readability.</p>
    </div>
  </header>

  <div class="nav" aria-hidden="true">
    <button type="button" onclick="document.querySelector('.carousel').scrollBy({left:-500,behavior:'smooth'})">◀︎ Prev</button>
    <button type="button" onclick="document.querySelector('.carousel').scrollBy({left:500,behavior:'smooth'})">Next ▶︎</button>
  </div>

  <section aria-label="Artwork by Danica" class="carousel">
    <figure class="slide">
      <img src="/assets/artists/danica/work-1.jpg" alt="Title of work 1 by Danica" width="1600" height="1066" loading="lazy">
      <figcaption>Work 1 — Medium, year.</figcaption>
    </figure>
    <figure class="slide">
      <img src="/assets/artists/danica/work-2.jpg" alt="Title of work 2 by Danica" width="1600" height="1066" loading="lazy">
      <figcaption>Work 2 — Medium, year.</figcaption>
    </figure>
    <figure class="slide">
      <img src="/assets/artists/danica/work-3.jpg" alt="Title of work 3 by Danica" width="1600" height="1066" loading="lazy">
      <figcaption>Work 3 — Medium, year.</figcaption>
    </figure>
    <!-- Optional additional slides up to 5 -->
  </section>
</div>
</body>
</html>
```

---

## 4) File Paths & Asset Names

```
/ artists/index.html              (the grid above)
/ artists/danica.html             (duplicate per artist)
/ assets/artists/danica/thumb.jpg
/ assets/artists/danica/work-1.jpg … work-5.jpg
/ assets/artists/david-v/thumb.jpg …
/ assets/artists/omi/thumb.jpg …
/ assets/artists/sofia/thumb.jpg …
/ assets/artists/leon/thumb.jpg …
```

---

## 5) Notes & A11y

- Keyboard users can Tab to each card (it’s a link). Focus ring is visible.
- `prefers-reduced-motion` disables hover float; layout remains static.
- Fallback when `clip-path` unsupported: rounded rectangles.
- All images include `alt`, intrinsic `width/height`, and `loading="lazy"`.
- Keep artist names short (≤14 chars) for clean wraps.

**Hook-up:** Change the 5 `href` values to your real artist pages and swap image paths. Duplicate the detail template for each artist.



---

## 6) `/artists` — Apply CTA Bar (Gmail compose links)

```html
<section class="apply-cta" aria-label="Apply to Chocolate & Art Show — Dallas">
  <div class="apply-inner">
    <h2>Artists • Vendors • Musicians — Apply</h2>
    <p>Spots are limited. Dallas is almost sold out — email us to be considered.</p>
    <div class="btns">
      <a class="btn" data-gmail to="<EMAIL>" data-subject="Dallas Artist Application — Sept 18–19" data-body="Name:%0D%0ALinks:%0D%0AMedium:%0D%0A">Artists apply</a>
      <a class="btn" data-gmail to="<EMAIL>" data-subject="Dallas Vendor Application — Sept 18–19" data-body="Name:%0D%0AProduct:%0D%0ALinks:%0D%0A">Vendors apply</a>
      <a class="btn" data-gmail to="<EMAIL>" data-subject="Dallas Musician Application — Sept 18–19" data-body="Name:%0D%0AGenre:%0D%0ALinks:%0D%0A">Musicians apply</a>
    </div>
  </div>
</section>
```

```css
.apply-cta{--bd:#ffffff22;--fg:#e9edf3;background:#0e0f12;border:1px solid var(--bd);border-radius:18px;padding:20px}
.apply-cta h2{margin:.1rem 0 .35rem;font-size:clamp(1.1rem,3.2vw,1.6rem)}
.apply-cta p{margin:0 0 .8rem;color:#b8c0cc}
.apply-cta .btns{display:flex;flex-wrap:wrap;gap:10px}
.apply-cta .btn{display:inline-block;padding:.7rem 1rem;border-radius:12px;background:#1d66ff;color:#fff;text-decoration:none;font-weight:800}
.apply-cta .btn:focus-visible{outline:3px solid #fff3}
@media (max-width:520px){.apply-cta .btn{flex:1 1 auto;text-align:center}}
```

```js
// Build Gmail compose URLs from data attributes; falls back to mailto if Gmail blocked
(function(){
  const q = s=>document.querySelectorAll(s);
  q('a[data-gmail]').forEach(a=>{
    const to = a.getAttribute('to');
    const sub = encodeURIComponent(a.dataset.subject||'');
    const body = a.dataset.body||'';
    const gmail = `https://mail.google.com/mail/?view=cm&fs=1&to=${encodeURIComponent(to)}&su=${sub}&body=${body}`;
    const mailto = `mailto:${to}?subject=${sub}&body=${body}`;
    a.href = gmail; a.target = '_blank';
    a.addEventListener('click',e=>{ if(!(window.location.hostname.includes('google')||navigator.userAgent.includes('Gmail'))){ a.href = gmail; }
      // If Gmail blocked (e.g., no login), let the browser fall back to mailto after a timeout
      setTimeout(()=>{ a.href = mailto; }, 200);
    });
  });
})();
```

---

## 7) `/tickets` — Mini Apply CTA Echo

```html
<section class="apply-mini" aria-label="Apply (Artists/Vendors/Musicians)">
  <a class="mini" data-gmail to="<EMAIL>" data-subject="Dallas Artist Application — Sept 18–19">Artists</a>
  <a class="mini" data-gmail to="<EMAIL>" data-subject="Dallas Vendor Application — Sept 18–19">Vendors</a>
  <a class="mini" data-gmail to="<EMAIL>" data-subject="Dallas Musician Application — Sept 18–19">Musicians</a>
</section>
```

```css
.apply-mini{display:flex;gap:8px;flex-wrap:wrap;margin:12px 0}
.apply-mini .mini{padding:.5rem .7rem;border:1px solid #ffffff33;border-radius:10px;color:#e8eefc;text-decoration:none;font-weight:700;background:#121419}
.apply-mini .mini:focus-visible{outline:3px solid #fff3}
```

---

## 8) Eventbrite Tracking — Attach to All Ticket Buttons

Use this pattern: add `data-eb-link` with the base Eventbrite URL and optional `data-eb-aff` & `data-eb-ref`. The script appends the correct tracking query and preserves existing params.

```html
<a class="btn buy" data-eb-link="https://www.eventbrite.com/e/123456789" data-eb-aff="dallas_site" data-eb-ref="header_cta">Get Tickets</a>
```

```js
(function(){
  const els = document.querySelectorAll('[data-eb-link]');
  els.forEach(el=>{
    const base = el.dataset.ebLink;
    const aff = el.dataset.ebAff||'dallas_site';
    const ref = el.dataset.ebRef||'site_cta';
    const url = new URL(base);
    url.searchParams.set('aff', aff);
    url.searchParams.set('ref', ref);
    el.href = url.toString();
    el.rel = 'noopener'; el.target = '_blank';
  });
})();
```

---

## 9) Hero Video @ 30s + Dark Overlay (self‑hosted)

```html
<section class="hero" aria-label="Dallas Hero">
  <div class="overlay" aria-hidden="true"></div>
  <video id="heroVid" playsinline muted preload="metadata" poster="/assets/dallas/poster.jpg">
    <source src="/assets/dallas/hero.mp4#t=30" type="video/mp4">
  </video>
  <div class="hero-copy">
    <h1>Chocolate & Art — Dallas</h1>
    <a class="btn buy" data-eb-link="https://www.eventbrite.com/e/123456789" data-eb-aff="dallas_site" data-eb-ref="hero">Tickets Sept 18–19</a>
  </div>
</section>
```

```css
.hero{position:relative;min-height:62vh;display:grid;place-items:center;overflow:hidden;background:#0b0c10}
.hero video{position:absolute;inset:0;width:100%;height:100%;object-fit:cover;filter:brightness(.55)}
.hero .overlay{position:absolute;inset:0;background:linear-gradient(#0008,#000b)}
.hero .hero-copy{position:relative;text-align:center;color:#eef2ff}
.hero h1{font-size:clamp(1.8rem,6vw,4rem);margin:0 0 12px}
```

```js
// Lock start at 30s (mobile safe) and honor reduced‑motion
(function(){
  const v = document.getElementById('heroVid');
  if(!v) return;
  if(window.matchMedia('(prefers-reduced-motion: reduce)').matches){ v.removeAttribute('autoplay'); v.pause(); return; }
  v.addEventListener('loadedmetadata', ()=>{ try{ v.currentTime = 30; }catch(e){} v.play().catch(()=>{}); });
})();
```

*YouTube alternative:* `https://www.youtube.com/embed/VIDEO_ID?start=30&autoplay=1&mute=1&controls=0` inside the same `.hero` wrapper.

---

## 10) RGB Big‑Type Interstitial — McShane “digipie” variant

```html
<!-- Drop anywhere as an interstitial section -->
<section class="rgb-digipie" aria-label="Big type interstitial">
  <!-- Optional: inline SVG noise filter (graceful fallback) -->
  <svg class="visually-hidden" aria-hidden="true" width="0" height="0" focusable="false">
    <filter id="noise-grain">
      <feTurbulence type="fractalNoise" baseFrequency="0.8" numOctaves="1" seed="7"/>
      <feColorMatrix type="saturate" values="0"/>
      <feComponentTransfer><feFuncA type="table" tableValues="0 0.12"/></feComponentTransfer>
    </filter>
  </svg>

  <div class="digipie" aria-hidden="true"></div>
  <h2>ONE OF THE LONGEST‑RUNNING ART SHOWS — 15 YEARS</h2>
</section>
```

```css
/* McShane-like conic-gradient “digipie” with ring mask and subtle grain */
.rgb-digipie{position:relative;min-height:40vh;display:grid;place-items:center;overflow:hidden;background:#0c0e12;padding:24px}
.rgb-digipie h2{position:relative;z-index:2;font-weight:900;letter-spacing:.02em;text-align:center;font-size:clamp(1.6rem,6.8vw,4.2rem);color:#eef2ff}
.rgb-digipie .digipie{position:absolute;inset:-35%;z-index:1;background:conic-gradient(from 0deg,#ff0040 0 90deg,#00e0ff 90deg 180deg,#7cff00 180deg 270deg,#ff0040 270deg 360deg);filter:blur(26px) saturate(1.25) contrast(1.05);opacity:.55;transform-origin:50% 50%;animation:digispin 22s linear infinite}
/* ring mask to create slices/spacing */
.rgb-digipie .digipie{-webkit-mask:radial-gradient(circle at 50% 50%,transparent 0 20%,#000 21% 25%,transparent 26% 34%,#000 35% 39%,transparent 40% 48%,#000 49% 53%,transparent 54% 62%,#000 63% 67%,transparent 68% 76%,#000 77% 81%,transparent 82% 100%);
                          mask:radial-gradient(circle at 50% 50%,transparent 0 20%,#000 21% 25%,transparent 26% 34%,#000 35% 39%,transparent 40% 48%,#000 49% 53%,transparent 54% 62%,#000 63% 67%,transparent 68% 76%,#000 77% 81%,transparent 82% 100%)}
/* optional SVG grain overlay on the whole section */
.rgb-digipie{background-image:radial-gradient(closest-side at 50% 50%,#0c0e1200, #0c0e12 70%)}
.rgb-digipie::after{content:"";position:absolute;inset:-1px;pointer-events:none;mix-blend-mode:overlay;opacity:.35;filter:url(#noise-grain)}
@keyframes digispin{to{transform:rotate(360deg)}}
/* Fallbacks */
@media (prefers-reduced-motion: reduce){.rgb-digipie .digipie{animation:none}}
@supports not (background: conic-gradient(#000,#111)){.rgb-digipie .digipie{display:none}}
```

## 11) `/tickets` — Night Selector with “Almost Gone”

```html
<section class="night-select" aria-label="Choose your night">
  <button class="night" data-date="Thu • Sept 18" data-eb-link="https://www.eventbrite.com/e/123456789" data-eb-ref="night_thu" data-status="low">
    Thu • Sept 18 <span class="tag" aria-hidden="true">Almost&nbsp;Gone</span>
  </button>
  <button class="night" data-date="Fri • Sept 19" data-eb-link="https://www.eventbrite.com/e/123456789" data-eb-ref="night_fri">
    Fri • Sept 19
  </button>
  <div class="sr" aria-live="polite" aria-atomic="true"></div>
</section>
```

```css
.night-select{display:flex;gap:10px;flex-wrap:wrap}
.night{appearance:none;border:1px solid #ffffff33;background:#121419;color:#eaf0ff;border-radius:12px;padding:.85rem 1.1rem;font-weight:900;cursor:pointer}
.night .tag{margin-left:.6rem;font-size:.75em;background:#ffd24a;color:#1a1200;padding:.2rem .45rem;border-radius:.6rem;font-weight:900}
.night[disabled]{opacity:.5;cursor:not-allowed}
```

```js
// Night buttons open tracked Eventbrite URLs; announce low inventory
(function(){
  const live = document.querySelector('.night-select .sr');
  document.querySelectorAll('.night-select .night').forEach(btn=>{
    const base = btn.dataset.ebLink; const ref = btn.dataset.ebRef||'night';
    const url = new URL(base); url.searchParams.set('aff','dallas_site'); url.searchParams.set('ref',ref);
    if(btn.dataset.status==='low'){ live.textContent = `${btn.dataset.date} is almost sold out.`; }
    btn.addEventListener('click',()=>{ window.open(url.toString(),'_blank','noopener'); });
  });
})();
```

---

## 12) Kusama Playground

### Teaser Card (place on `/artists` under the grid)

```html
<a class="kusama-card" href="/play/kusama" aria-label="Open the Kusama dot playground">
  <figure><img src="/assets/play/kusama-thumb.jpg" alt="Preview of Kusama dot playground" loading="lazy" width="1280" height="720"></figure>
  <span>Play with the dots →</span>
</a>
```

```css
.kusama-card{display:grid;grid-template-columns:140px 1fr;gap:12px;align-items:center;padding:12px;border:1px dashed #ffffff33;border-radius:14px;color:#e8eefc;text-decoration:none}
.kusama-card figure{margin:0;border-radius:10px;overflow:hidden}
.kusama-card img{display:block;width:100%;height:auto}
.kusama-card span{font-weight:800}
```

### `/play/kusama/index.html` (lightweight canvas demo)

```html
<!doctype html><html lang="en"><meta charset="utf-8"><meta name="viewport" content="width=device-width,initial-scale=1">
<title>Kusama Playground — Chocolate & Art</title>
<style>html,body{height:100%;margin:0;background:#0b0c10;color:#e9eef7;font:16px/1.5 system-ui}canvas{display:block;width:100%;height:100%}</style>
<canvas id="k"></canvas>
<script>
const c=document.getElementById('k');const x=c.getContext('2d');
function size(){c.width=innerWidth; c.height=innerHeight;} size(); addEventListener('resize',size);
const dots=[...Array(180)].map(()=>({x:Math.random()*c.width,y:Math.random()*c.height,r:2+Math.random()*5,dx:(Math.random()-.5)*.6,dy:(Math.random()-.5)*.6}));
function draw(){x.clearRect(0,0,c.width,c.height);dots.forEach(d=>{x.beginPath();x.arc(d.x,d.y,d.r,0,Math.PI*2);x.fillStyle='hsl('+((d.x+d.y)%360)+',80%,60%)';x.fill();d.x+=d.dx;d.y+=d.dy;if(d.x<0||d.x>c.width)d.dx*=-1;if(d.y<0||d.y>c.height)d.dy*=-1});requestAnimationFrame(draw)}
if(!matchMedia('(prefers-reduced-motion: reduce)').matches){draw()}
</script>
</html>
```

---

### Integration Notes

- Paste sections into their respective pages (`/artists`, `/tickets`, homepage/Dallas landing) and include the small JS helpers on all pages (/, /artists, every /artists/.html, /tickets, and /play/kusama).
- All snippets include keyboard focus states and `prefers-reduced-motion` fallbacks.
- Replace placeholder emails if needed; the Gmail builder will still work.
- Ticket links automatically append your Eventbrite `aff/ref` tracking.

