# TREE — Visual Style  

> Where each effect lives, why we use it, and guardrails.&#x20;
>
> ## GLOBAL (site‑wide)

- **Background video layer (section‑specific)**
  - Use on **Home hero** or **one showcase section** only. Keep all other sections static.
  - Source: official YouTube video. Start **30 seconds in** to skip white intro.

**Snippet (YouTube background, starts at 30s)**

```html
<div class="video-bg">
  <iframe src="https://www.youtube.com/embed/VIDEO_ID?start=30&autoplay=1&mute=1&loop=1&playlist=VIDEO_ID&controls=0&rel=0&playsinline=1" allow="autoplay" frameborder="0"></iframe>
</div>
<div class="video-overlay"></div>
```

```css
.video-bg{position:fixed;inset:0;z-index:-2;pointer-events:none}
.video-bg iframe{position:absolute;top:50%;left:50%;width:177.777vh;min-width:100vw;height:100vh;transform:translate(-50%,-50%)}
.video-overlay{position:fixed;inset:0;background:rgba(0,0,0,.55);z-index:-1}
```

- **Background:** Use a subtle textured black (not flat). Keeps white type & artwork crisp; reduces eye fatigue.

**Snippet (global background):**

```css
:root{--noise:url("data:image/svg+xml,%3Csvg viewBox='0 0 250 250' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='6.29' numOctaves='6' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");}
html,body{height:100%}
body{background:#0b0b0d;margin:0;position:relative}
body::after{content:"";position:fixed;inset:0;pointer-events:none;background:var(--noise);opacity:.38;mix-blend-mode:overlay}
```

*Place in global CSS. Keep cards slightly lighter (#111–#151). No motion; works with reduced‑motion.*

- **Typography:** Headlines bold/tight; body ≥16px. Maintain WCAG AA contrast.

- **Motion hygiene:** Every animated section must have a static fallback and respect `prefers-reduced-motion`.

- **Background:** Use a subtle textured black (not flat). Keeps white type & artwork crisp; reduces eye fatigue.

**Snippet (global background):**

```css
:root{--noise:url("data:image/svg+xml,%3Csvg viewBox='0 0 250 250' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='6.29' numOctaves='6' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");}
html,body{height:100%}
body{background:#0b0b0d;margin:0;position:relative}
body::after{content:"";position:fixed;inset:0;pointer-events:none;background:var(--noise);opacity:.38;mix-blend-mode:overlay}
```

*Place in global CSS. Keep cards slightly lighter (#111–#151). No motion; works with reduced‑motion.* 

- **Typography:** Headlines bold/tight; body ≥16px. Maintain WCAG AA contrast.
- **Motion hygiene:** Every animated section must have a static fallback and respect `prefers-reduced-motion`.

## TICKET LINK STANDARD (Eventbrite)

Use this exact pattern for **all ticket buttons/links**. This is the only off‑site destination.

```
https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign={slot}&discount={{PROMO_CODE}}
```

- Replace `{slot}` with the placement (e.g., `hero`, `rgbwall`, `ticketsbox`, `footer`).
- If no promo is active, remove `&discount={{PROMO_CODE}}`.
- Keep `target="_blank" rel="noopener"`.

## HOME

### INTERSTITIAL — RGB Big‑Type Wall (transition band)

**Placement**

- Full‑bleed **transition** between **“What is Chocolate & Art?”** and **Gallery Teaser**, or between **Artists** and **Tickets**.
- Purpose: re‑capture attention with a bold message and a soft CTA.

**Copy ideas**

- **ART + MUSIC + CHOCOLATE = DALLAS NIGHTS**
- **LOCAL + EMERGING + LIVE = CHOCO•ART**

**Snippet**

```html
<section class="rgb-wall" aria-label="Intermission banner">
  <div class="rgb-hero">
    <span class="rgb-word" data-txt="ART">ART</span>
    <span class="rgb-plus">+</span>
    <span class="rgb-word" data-txt="MUSIC">MUSIC</span>
    <span class="rgb-plus">+</span>
    <span class="rgb-word" data-txt="CHOCOLATE">CHOCOLATE</span>
    <span class="rgb-eq">=</span>
    <span class="rgb-word" data-txt="DALLAS">DALLAS</span>
  </div>
  <a class="rgb-cta" href="https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=rgbwall&discount={{PROMO_CODE}}" target="_blank" rel="noopener">Buy Tickets</a>
</section>
```

```css
.rgb-wall{isolation:isolate;min-height:58vh;display:grid;place-items:center;gap:2rem;padding:8vh 4vw;background:#0f1114}
.rgb-hero{display:flex;flex-wrap:wrap;align-items:center;gap:clamp(.4rem,2vw,1.2rem)}
.rgb-plus,.rgb-eq{color:#8ea3b3;font-weight:800;font-size:clamp(2rem,8vw,6rem)}
.rgb-word{position:relative;display:inline-block;font-weight:900;text-transform:uppercase;letter-spacing:-.02em;line-height:.85;color:#eaeaea;font-size:clamp(3rem,14vw,12rem)}
.rgb-word::before,.rgb-word::after{content:attr(data-txt);position:absolute;inset:0;z-index:-1}
.rgb-word::before{transform:translate(.18em,.06em);color:#e53935;opacity:.8}
.rgb-word::after{transform:translate(-.14em,.08em);color:#03a9f4;opacity:.75}
.rgb-cta{display:inline-block;font-weight:900;letter-spacing:.04em;padding:.9rem 1.25rem;border-radius:12px;border:1px solid #ffffff3a;background:#ffffff12;color:#fff;text-decoration:none}
```

---

### SNIPPET — Gradient Text Shadow CTA (pure CSS)

Use for the **primary CTA headline** on Home and /tickets; short words only.

```html
<!-- Head (one-time) -->
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Prompt:wght@800&display=swap" rel="stylesheet">
```

```css
/* CTA headline treatment */
.cta-gradient{font-family:"Prompt",system-ui,sans-serif;font-weight:800;line-height:.9;margin:0;
  background:linear-gradient(4deg,#548cff 10%,#f900bf 90%);
  -webkit-text-fill-color:transparent;-webkit-background-clip:text;background-clip:text;
  --shadow-size:.08em;padding:var(--shadow-size) 0 0 var(--shadow-size);
  text-shadow:calc(-1*var(--shadow-size)) calc(-1*var(--shadow-size)) #fff;
  font-size:clamp(2.6rem,12vw,7rem)}
```

```html
<h2 class="cta-gradient">Buy Dallas Tickets</h2>
```

### Hero (first screen)

### BIG TYPE — ArgyleInk “Large Letter Stack” (Pen NWRaLKm)

**Best fit**

- **Hero sub‑variant** on landing (alternate to waterfall) or **section breakers** (“ART • MUSIC • CHOCOLATE”).
- Use for **very short words** (1–3 words per line). The point is scale + spacing; don’t wrap mid‑word. **Notes**
- Tight line height (≈0.82–0.9), slight negative letter‑spacing, all‑caps, ultra‑bold.
- Keep contrast high; outline variant works well on textured black. **Snippet (structure & styles)**

```html
<h1 class="bigtext">
  <span class="line">CHOCOLATE</span>
  <span class="line">& ART</span>
  <span class="line">DALLAS</span>
</h1>
```

```css
.bigtext{font-weight:900;text-transform:uppercase;line-height:.82;letter-spacing:-.02em;
  font-size:clamp(3rem,14vw,18rem);margin:0}
.bigtext .line{display:block}
/* Variant A: gradient fill */
.bigtext.-gradient{background:linear-gradient(180deg,#fff,#d9d9d9);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}
/* Variant B: outline (knockout) */
.bigtext.-outline{-webkit-text-fill-color:transparent;-webkit-text-stroke:.08em #fff}
```

### SNIPPET — Gradient Text Shadow CTA (pure CSS)

Use for the **primary CTA headline** on Home and /tickets; short words only.

```html
<!-- Head (one-time) -->
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Prompt:wght@800&display=swap" rel="stylesheet">
```

```css
/* CTA headline treatment */
.cta-gradient{font-family:"Prompt",system-ui,sans-serif;font-weight:800;line-height:.9;margin:0;
  background:linear-gradient(4deg,#548cff 10%,#f900bf 90%);
  -webkit-text-fill-color:transparent;-webkit-background-clip:text;background-clip:text;
  --shadow-size:.08em;padding:var(--shadow-size) 0 0 var(--shadow-size);
  text-shadow:calc(-1*var(--shadow-size)) calc(-1*var(--shadow-size)) #fff;
  font-size:clamp(2.6rem,12vw,7rem)}
```

```html
<h2 class="cta-gradient">Buy Dallas Tickets</h2>
```

### Hero (first screen)

- **Waterfall/Perspective Headline (scroll‑linked)** — 2–3 short lines max. Purpose: poster‑energy + scroll engagement. Fallback: static stacked headline on small screens or with reduced motion.

**SNIPPET — Waterfall/Perspective Headline (skeleton)** Use for the hero headline. Replace text; keep it brief for mobile.

```html
<div class="waterfall" aria-label="Chocolate and Art Show Dallas">
  <span class="line">Chocolate &</span>
  <span class="line">Art Show</span>
  <span class="line">Dallas</span>
</div>
```

```css
/* 3D tilt + simple extrusion */
.waterfall{transform-origin:left top;transform:perspective(720px) rotateX(55deg) rotateZ(-28deg) skewX(-8deg)}
.waterfall .line{display:block;font-weight:900;text-transform:uppercase;font-size:clamp(2rem,14vw,8rem);line-height:.9;color:#fff;
  text-shadow:1px 1px 0 #fff,2px 2px 0 #fff,3px 3px 0 #fff,4px 4px 0 #fff,5px 5px 0 #fff,6px 6px 0 #fff,7px 7px 0 #fff,8px 8px 0 #fff,9px 9px 0 #fff,10px 10px 0 #fff,11px 11px 0 #fff,12px 12px 0 #fff}
/* Scroll‑linked cascade (progressive enhancement) */
@keyframes drift{to{transform:translateY(var(--y,0))}}
@supports (animation-timeline: scroll(root block)){
  .waterfall .line{animation:drift linear both;animation-timeline:scroll(root block);animation-range:entry 10% cover 80%}
  .waterfall .line:nth-child(1){--y:6vh}
  .waterfall .line:nth-child(2){--y:12vh}
  .waterfall .line:nth-child(3){--y:18vh}
}
```

*Guardrails:* high contrast only; disable on very small screens or when `prefers-reduced-motion`.

### Night Selector (Fri / Sat) (Fri / Sat)

- Small **“Almost Gone”** / **“Low Inventory”** label may use the gradient treatment. Keep it short; do not style the buttons themselves.

### “What is Chocolate & Art?” (EXPLORE / DISCOVER / IMAGINE)

- **Layout:** Three‑column grid mirroring your screenshot.
  - **EXPLORE (left):** stacked manifesto lines; tight line height (1.05–1.15).
  - **DISCOVER (middle):** small section label + quick tabs (Artists · Music · Chocolate).
  - **IMAGINE (right):** generative/typography visual panel. May preview the “trippy refresh” art but keep a dark overlay; copy must dominate here.

### Gallery Teaser

- No special effects; clean grid. Let art breathe.

### Social Proof / UGC

- Static thumbnails first; avoid motion to maintain scroll rhythm.

### PRIMARY LANDING HEADLINE — KyleShook Retro Long‑Shadow (Pen LvgWPY)

**Best fit**

- **Main hero headline** on landing page (our default), with Gradient CTA beneath. **Notes**
- Long shadow gives dimensional “poster” vibe. Keep to 1–2 lines.
- Strong white text over textured black; angle shadows down/right. **Snippet (retro long‑shadow)**

```html
<h1 class="retro">Chocolate & Art — Dallas</h1>
```

```css
.retro{font-weight:900;text-transform:uppercase;color:#fff;line-height:.9;margin:0;
  font-size:clamp(3rem,12vw,10rem);
  text-shadow:
    1px 1px 0 #f900bf, 2px 2px 0 #ef00c1, 3px 3px 0 #e400c5, 4px 4px 0 #d600cb,
    5px 5px 0 #c400d2, 6px 6px 0 #ac00db, 7px 7px 0 #9400e4, 8px 8px 0 #7b00ec,
    9px 9px 0 #6502f0, 10px 10px 0 #4f1ff3, 11px 11px 0 #3e39f6, 12px 12px 0 #2f53f8,
    13px 13px 0 #246cf9, 14px 14px 0 #1f7ef9, 15px 15px 0 #218ff8, 16px 16px 0 #309cf6;
}
```

*Guardrails:* limit to short headings; ensure sufficient contrast; provide a plain bold variant as fallback.

### TYPOGRAPHIC BOX MODULES — Framed Cards (like your screenshot)

**Purpose**

- Convey manifesto‑style lines with **hierarchy inside a frame**: small label → huge word → small aside → italic question/line. **Where**
- Home (below hero), **About** stripe, or **Artist Spotlight** blurb. **Rules**
- Thick inner frame (8–14px), generous padding, centered or left‑aligned type. Color glow optional; focus on layout + size contrast. **Snippet (structure & styles)**

```html
<article class="typebox">
  <div class="cap">DALLAS</div>
  <h2 class="lead">MUSIC</h2>
  <div class="minor">an <strong>ART</strong>,</div>
  <p class="q">or just <em>SHOW</em>?</p>
</article>
```

```css
.typebox{--frame:#eef3d5;--bg:#1b1b1c;position:relative;background:var(--bg);border:12px solid var(--frame);
  border-radius:10px;padding:clamp(16px,3vw,28px) clamp(18px,4vw,36px);max-width:980px;margin-inline:auto;
  box-shadow:0 0 0 1px rgba(255,255,255,.06) inset, 0 24px 60px rgba(0,0,0,.45)}
.typebox .cap{font-weight:800;letter-spacing:.08em;font-size:clamp(12px,1.8vw,22px)}
.typebox .lead{margin:.2em 0 0;font-weight:900;text-transform:uppercase;line-height:.9;font-size:clamp(42px,9vw,120px)}
.typebox .minor{margin:.35em 0 0;font-weight:600;font-size:clamp(14px,2.4vw,28px)}
.typebox .q{margin:.35em 0 0;font-style:italic;font-size:clamp(20px,4vw,44px)}
/* Optional soft glow (use sparingly) */
.typebox::after{content:"";position:absolute;inset:-12px;pointer-events:none;border-radius:14px;opacity:.18;
  background:radial-gradient(60% 80% at 50% 20%,#f9ffb0,transparent 60%),
             radial-gradient(60% 80% at 20% 80%,#37d0b6,transparent 60%),
             radial-gradient(60% 80% at 80% 80%,#ff5ea8,transparent 60%)}
```

*Copy guidance:* Keep each line short. Mix caps (LEAD) + italics (question). Avoid wrapping the huge word.

### SNIPPET — Gradient Text Shadow CTA (pure CSS)

Use for the **primary CTA headline** on Home and /tickets; short words only.

```html
<!-- Head (one-time) -->
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Prompt:wght@800&display=swap" rel="stylesheet">
```

```css
/* CTA headline treatment */
.cta-gradient{font-family:"Prompt",system-ui,sans-serif;font-weight:800;line-height:.9;margin:0;
  background:linear-gradient(4deg,#548cff 10%,#f900bf 90%);
  -webkit-text-fill-color:transparent;-webkit-background-clip:text;background-clip:text;
  --shadow-size:.08em;padding:var(--shadow-size) 0 0 var(--shadow-size);
  text-shadow:calc(-1*var(--shadow-size)) calc(-1*var(--shadow-size)) #fff;
  font-size:clamp(2.6rem,12vw,7rem)}
```

```html
<h2 class="cta-gradient">Buy Dallas Tickets</h2>
```

### Hero (first screen)

- **Waterfall/Perspective Headline (scroll‑linked)** — 2–3 short lines max. Purpose: poster‑energy + scroll engagement. Fallback: static stacked headline on small screens or with reduced motion.

**SNIPPET — Waterfall/Perspective Headline (skeleton)** Use for the hero headline. Replace text; keep it brief for mobile.

```html
<div class="waterfall" aria-label="Chocolate and Art Show Dallas">
  <span class="line">Chocolate &</span>
  <span class="line">Art Show</span>
  <span class="line">Dallas</span>
</div>
```

```css
/* 3D tilt + simple extrusion */
.waterfall{transform-origin:left top;transform:perspective(720px) rotateX(55deg) rotateZ(-28deg) skewX(-8deg)}
.waterfall .line{display:block;font-weight:900;text-transform:uppercase;font-size:clamp(2rem,14vw,8rem);line-height:.9;color:#fff;
  text-shadow:1px 1px 0 #fff,2px 2px 0 #fff,3px 3px 0 #fff,4px 4px 0 #fff,5px 5px 0 #fff,6px 6px 0 #fff,7px 7px 0 #fff,8px 8px 0 #fff,9px 9px 0 #fff,10px 10px 0 #fff,11px 11px 0 #fff,12px 12px 0 #fff}
/* Scroll‑linked cascade (progressive enhancement) */
@keyframes drift{to{transform:translateY(var(--y,0))}}
@supports (animation-timeline: scroll(root block)){
  .waterfall .line{animation:drift linear both;animation-timeline:scroll(root block);animation-range:entry 10% cover 80%}
  .waterfall .line:nth-child(1){--y:6vh}
  .waterfall .line:nth-child(2){--y:12vh}
  .waterfall .line:nth-child(3){--y:18vh}
}
```

*Guardrails:* high contrast only; disable on very small screens or when `prefers-reduced-motion`.

### Night Selector (THUR / FRI ) (THUR / FRI)

- Small **“Almost Gone”** / **“Low Inventory”** label may use the gradient treatment. Keep it short; do not style the buttons themselves.

### “What is Chocolate & Art?” (EXPLORE / DISCOVER / IMAGINE)

- **Layout:** Three‑column grid mirroring your screenshot.
  - **EXPLORE (left):** stacked manifesto lines; tight line height (1.05–1.15).
  - **DISCOVER (middle):** small section label + quick tabs (Artists · Music · Chocolate).
  - **IMAGINE (right):** generative/typography visual panel. May preview the “trippy refresh” art but keep a dark overlay; copy must dominate here.

### Gallery Teaser

- No special effects; clean grid. Let art breathe.

### Social Proof / UGC

- Static thumbnails first; avoid motion to maintain scroll rhythm.

## /TICKETS

### SNIPPET — CTA Box (from mcShane variant)

**Placement**

- Top of **/tickets** under the hero and again above the pricing grid.
- Purpose: compact, high‑contrast CTA with subtle animated border.

**Snippet**

```html
<a class="cta-box" href="https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=ticketsbox&discount={{PROMO_CODE}}" target="_blank" rel="noopener">
  <span class="cta-title">Buy Dallas Tickets</span>
  <span class="cta-sub">Two nights • 21+ • Lofty Spaces</span>
</a>
```

```css
.cta-box{--c1:#7f5af0;--c2:#2cb67d;--bg:#0b0b0d;display:grid;gap:.25rem;justify-items:center;text-align:center;
  padding:1rem 1.25rem;border-radius:14px;color:#fff;text-decoration:none;font-weight:800;
  background:linear-gradient(#ffffff10,#ffffff10) padding-box,
             conic-gradient(from var(--ang,0deg),var(--c1),var(--c2),var(--c1)) border-box;
  border:2px solid transparent}
.cta-box:hover{--ang:180deg}
.cta-title{font-size:clamp(1.2rem,4.2vw,2rem);letter-spacing:.02em}
.cta-sub{font-weight:600;opacity:.85;font-size:.95rem}
@media (prefers-reduced-motion:reduce){.cta-box{background:linear-gradient(#ffffff10,#ffffff10) padding-box,#6b6b6b33 border-box}}
```

- **Top CTA headline** may use Gradient Text Shadow. All other text remains standard for readability.

- **Urgency strip** (countdown/last entry) is plain high‑contrast text—no special effects.

- **Top CTA headline** may use Gradient Text Shadow. All other text remains standard for readability.

- **Urgency strip** (countdown/last entry) is plain high‑contrast text—no special effects.

## /ARTISTS

### APPLY (Artists • Vendors • Musicians) — Email Only

**Where**: Add this block near the top of **/artists** and mirror a smaller version on **/tickets**. **Why**: Almost sold out; keep intake lightweight. No form yet—email only. **Address**: [<EMAIL>](mailto\:<EMAIL>)

**Snippet (CTA row — opens Gmail compose with prefilled subject/body)**

```html
<section class="apply-row" aria-label="Apply by email">
  <a class="apply-cta" target="_blank" rel="noopener"
     href="https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su=Artist%20Submission%20%E2%80%94%20Dallas%20Sept%2018%E2%80%9319&body=Name:%0AInstagram:%0APortfolio:%0AMedium/Style:%0ANight%20preference%20(Thu/Fri/Either):%0ANotes:%0A">Artists: Apply Here</a>

  <a class="apply-cta" target="_blank" rel="noopener"
     href="https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su=Vendor%20Application%20%E2%80%94%20Dallas%20Sept%2018%E2%80%9319&body=Business%20name:%0AProduct%20category:%0AWebsite/Instagram:%0APower%20needs:%0ANight%20preference:%0ANotes:%0A">Vendors: Apply Here</a>

  <a class="apply-cta" target="_blank" rel="noopener"
     href="https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su=Musician%2FDJ%20Application%20%E2%80%94%20Dallas%20Sept%2018%E2%80%9319&body=Act%20name:%0AGenre:%0ALinks%20(YouTube/SoundCloud/Instagram):%0ATech%20rider%20(basic):%0AAvailability%20(Thu/Fri):%0ANotes:%0A">Musicians/DJs: Apply Here</a>
</section>
```

```css
.apply-row{display:grid;grid-template-columns:repeat(auto-fit,minmax(240px,1fr));gap:14px;margin:18px 0}
.apply-cta{display:flex;align-items:center;justify-content:center;text-align:center;padding:.9rem 1.1rem;border-radius:12px;font-weight:800;color:#fff;text-decoration:none;border:1px solid #ffffff33;background:#ffffff12}
.apply-cta:hover{background:#ffffff1f}
```

**Microcopy**: “Space is extremely limited and nearly sold out. Email now to be considered—complete submissions receive priority.”

- **Header Band:** Use the **“trippy refresh”** background (random each refresh) as a 400–600px living canvas behind the page title and filters.
  - Always overlay a **90–95% dark gradient** and a **subtle noise layer** for legibility.
  - Respect `reduced-motion`: swap to a static image when motion is off.

**SNIPPET — Artists Header “Trippy Refresh” (refresh‑randomized banner)**

> Lightweight approximation (CSS first, optional 2‑line JS to randomize hue/angle per refresh). Keep text on top; add a dark overlay.

```html
<header class="artists-hero">
  <h1>Artists</h1>
</header>
```

```css
.artists-hero{position:relative;min-height:48vh;border-radius:20px;overflow:hidden;display:grid;place-items:end start;padding:2.5rem}
/* Dark overlay + animated‑looking conic gradient seeded by CSS vars */
.artists-hero{background:
  linear-gradient(rgba(0,0,0,.92),rgba(0,0,0,.92)),
  conic-gradient(from var(--a,0deg), hsl(var(--h,210) 90% 55%), hsl(calc(var(--h,210)+120) 90% 55%), hsl(calc(var(--h,210)+240) 90% 55%), hsl(var(--h,210) 90% 55%))}
/* Add subtle noise for tooth */
.artists-hero::after{content:"";position:absolute;inset:0;pointer-events:none;opacity:.25;mix-blend-mode:overlay;
  background:radial-gradient(1px 1px at 20% 10%,rgba(255,255,255,.05),transparent 40%),radial-gradient(1px 2px at 80% 30%,rgba(255,255,255,.04),transparent 45%)}
.artists-hero h1{margin:0;font-weight:900;font-size:clamp(2rem,6vw,4rem);color:#fff}
@media (prefers-reduced-motion: reduce){.artists-hero{background:linear-gradient(rgba(0,0,0,.94),rgba(0,0,0,.94)),#0b0b0d}}
```

```html
<!-- Optional: randomize hue/angle on each refresh (drop once, anywhere after load) -->
<script>
  document.documentElement.style.setProperty('--h', Math.floor(Math.random()*360));
  document.documentElement.style.setProperty('--a', Math.floor(Math.random()*360)+'deg');
</script>
```

- **Spotlight Stripe (mid‑page):** a narrow horizontal banner can reuse the “trippy refresh” with a heavier dark overlay.
- **Artist Cards/List:** No animated backgrounds; keep focus on artwork. Gentle hover only.

## /GALLERY

- Use only the global textured black background. Minimal chrome in lightbox.

## /SCHEDULE

- Plain, legible grid. No perspective/gradient effects.

## /FAQ

- Static accordions. Clarity over style; icons only when they aid scanning.

## /CONTACT

- Map + rideshare pin; keep the global textured backdrop; no animated backgrounds.

## DECK / SOCIAL ASSETS (not the site)

- **Waterfall/Perspective Headline:** Slide openers and short Reels bumpers.
- **Gradient Text Shadow:** Single punch word (e.g., “TONIGHT”, “SOLD OUT”). Avoid paragraphs.

## GUARDRAILS

- Do not place animated backgrounds behind long text sections (FAQ, schedules, long artist lists).
- Limit Gradient Text Shadow to **one headline per page**.
- Avoid perspective/waterfall on devices under \~360px width or when reduced‑motion is set.

## METRICS TO WATCH

- Scroll‑stop dwell on Waterfall Hero vs control.
- CTA CTR when Gradient style is on vs off.
- Bounce/time‑on‑page for /artists with “trippy refresh” on vs static header.



---

## SNIPPET — Subtle Textured Background (extracted from Stoumann Pen)

Use for the **global page background**. This is the grain/noise overlay technique from the CodePen you shared, adapted to our dark theme.

```css
/* Source idea: Stoumann — noise overlay (data: SVG feTurbulence) */
:root{
  --noise: url("data:image/svg+xml,%3Csvg viewBox='0 0 250 250' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='6.29' numOctaves='6' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}
html,body{height:100%}
body{
  background:#0b0b0d; /* deep neutral black */
  margin:0; position:relative;
}
body::after{
  content:""; position:fixed; inset:0; pointer-events:none;
  background:var(--noise);
  opacity:.38; /* tweak .30–.45 depending on art */
  mix-blend-mode:overlay; /* alternative: soft-light */
}
```

**Placement:** put in the global CSS (loads once). Keep card surfaces slightly lighter (#111–#151) with a faint 1px border (`rgba(255,255,255,.06)`) so art and copy pop.

**Accessibility/Perf:** static, GPU‑cheap; no motion. Works even when `prefers-reduced-motion` is enabled.



---

# STYLE PACK — Zephyo (4 Headline/CTA Treatments)

> Pulled from the reference set you shared. Each includes placement, why it fits, and a ready snippet.

## 1) Neon Sign Headline

**Where**: **Home hero (night vibe)** or late‑phase promo banner; also works for stage screens. **Why**: Instant nightlife signal; high contrast; reads from distance.

```html
<h1 class="neon">CHOCOLATE AND ART SHOW</h1>
```

```css
.neon{font-weight:800;letter-spacing:.18em;text-transform:uppercase;color:#fff;
  font-size:clamp(2.6rem,8vw,6rem);
  text-shadow:0 0 .08em #ff6bd6,0 0 .2em #ff6bd6,0 0 .4em #ff6bd6,0 0 .8em #a000ff,0 0 1.6em #a000ff}
```

## 2) Sticker Rainbow Outline (poster-y “sticker”)

**Where**: **Interstitial wall** or a fun alt in the Artists area; sparingly on hero. **Why**: Playful print‑poster energy; great for single line names or city tags.

```html
<h2 class="sticker" data-text="Chocolate and Art Show">Chocolate and Art Show</h2>
```

```css
.sticker{position:relative;display:inline-block;font-weight:900;text-transform:capitalize;font-size:clamp(2rem,7vw,5rem);color:#222}
.sticker::before{content:attr(data-text);position:absolute;inset:0;z-index:-1;color:transparent;
  text-shadow: 0 0 0 #fff, 2px 2px 0 #fff, -2px -2px 0 #fff, 4px 4px 0 #8ef, -4px -4px 0 #f9a, 6px 6px 0 #8f6, -6px -6px 0 #fd6}
```

## 3) iMessage‑Style FOMO Chat (texting blob)

**Where**: **Just above Tickets** or under Night Selector. **Why**: Social proof + urgency in a friendly voice. Keep to 3–4 bubbles.

```html
<section class="imessage" aria-label="FOMO chat">
  <p class="from">Thu — The Dallas Stones are playing.</p>
  <p class="to">Friday is DJ Omu.</p>
  <p class="from">Then we better go both nights.</p>
  <p class="to">Hope we can get in — it’s almost sold out. <a href="https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=imessage&discount={{PROMO_CODE}}" target="_blank" rel="noopener">Grab tickets</a></p>
</section>
```

```css
.imessage{max-width:680px;margin:0 auto;padding:12px 16px;font:500 16px/1.4 system-ui,-apple-system,Segoe UI,Roboto,Arial}
.imessage p{max-width:80%;padding:10px 14px;border-radius:18px;margin:8px 0;display:inline-block;position:relative}
.imessage .from{background:#e5e5ea;color:#000}
.imessage .to{background:#0b93f6;color:#fff;margin-left:auto;display:block}
.imessage .from::before{content:"";position:absolute;left:-6px;bottom:0;width:12px;height:12px;background:#e5e5ea;border-bottom-right-radius:12px}
.imessage .to::after{content:"";position:absolute;right:-6px;bottom:0;width:12px;height:12px;background:#0b93f6;border-bottom-left-radius:12px}
.imessage a{color:#fff;text-decoration:underline;font-weight:700}
```

## 4) Embossed Plaque Title

**Where**: **“Know Before You Go”** header or **Tickets** side notice (21+, last entry times). **Why**: Authority tone; reads like a venue placard.

```html
<h3 class="plaque">Two Nights Only — Limited Capacity</h3>
```

```css
.plaque{--b1:#dcdcdc;--b2:#8f8f8f;background:#7d7d7d;color:#fff;padding:.6em .9em;border-radius:10px;font-style:italic;font-weight:700;
  font-size:clamp(1.4rem,4.8vw,2.6rem);border:8px solid var(--b1);box-shadow:inset 0 0 0 8px var(--b2)}
```

**Copy pairings**

- **Neon** → “CHOCOLATE AND ART SHOW”
- **Sticker** → “Dallas, we’re back” or city/date slugs
- **Chat** → four‑bubble FOMO exchange ending with the ticket link
- **Plaque** → “21+ • Last entry 12:30 AM • ID required”

