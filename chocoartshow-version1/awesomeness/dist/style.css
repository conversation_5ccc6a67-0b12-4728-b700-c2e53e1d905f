@import url(https://fonts.googleapis.com/css?family=Montserrat:700);
@import url(https://fonts.googleapis.com/css?family=Oswald:300);
body {
  background: #333;
  height: 100vw;
  overflow-x: hidden;
}

.html,
.css,
.js {
  font-family: "Montserrat", sans-serif;
  font-size: 200px;
  opacity: 0.6;
  width: 100vw;
  height: 200px;
  position: absolute;
  transition: all 1s ease-out;
}
.html.active,
.css.active,
.js.active {
  opacity: 0;
  transition: all 1s ease-out;
}
.html .char1,
.css .char1,
.js .char1 {
  position: absolute;
  color: #e74c3c;
  z-index: 10;
  text-shadow: 10px 0 15px #333;
}
.html .char2,
.css .char2,
.js .char2 {
  position: absolute;
  color: #1abc9c;
  z-index: 8;
  left: 110px;
  text-shadow: 10px 0 15px #333;
}
.html .char3,
.css .char3,
.js .char3 {
  position: absolute;
  color: #299ef9;
  z-index: 5;
  left: 190px;
  text-shadow: 7.5px 0 15px #333;
}
.html .char4,
.css .char4,
.js .char4 {
  position: absolute;
  color: #D0DB97;
  left: 340px;
}
.html .char6,
.css .char6,
.js .char6 {
  position: absolute;
  color: #B2D7E0;
  left: 480px;
}

.html {
  position: fixed;
  top: 0;
}

.css {
  position: absolute;
  top: 32.5vh;
}
.css .char5 {
  position: absolute;
  color: #B2D7E0;
  left: 480px;
}

.js {
  position: absolute;
  top: 65vh;
}
.js .char2 {
  left: 70px;
}
.js .char4 {
  position: absolute;
  color: #B2D7E0;
  left: 480px;
}

.awesomeness {
  transition: all 1s ease-out;
  font-family: "Oswald", sans-serif;
  position: absolute;
  top: 100vh;
  font-size: 180px;
  color: #eee;
  opacity: 0;
  text-transform: uppercase;
}
.awesomeness.active {
  opacity: 1;
  transition: all 1s ease-out;
}

.stickdiv {
  position: fixed;
  top: 0;
}

/*# sourceMappingURL=style.css.map */
