{"name": "awesomeness-project", "version": "1.0.0", "description": "HTML + CSS + JS = Awesomeness animation project", "main": "dist/index.html", "scripts": {"build": "npm run build:scss && npm run build:js", "build:scss": "sass src/style.scss dist/style.css", "build:js": "cp src/script.js dist/script.js", "watch": "npm run watch:scss", "watch:scss": "sass --watch src/style.scss:dist/style.css", "serve": "python3 -m http.server 8080 --directory dist", "dev": "npm run build && npm run serve", "start": "npm run dev"}, "devDependencies": {"sass": "^1.69.0"}, "keywords": ["haml", "scss", "animation", "web"], "author": "", "license": "MIT"}