{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_5901b7c6.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_5901b7c6-module__ec5Qua__className\",\n  \"variable\": \"inter_5901b7c6-module__ec5Qua__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_5901b7c6.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,gKAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,gKAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,gKAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/prompt_df9adab7.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"prompt_df9adab7-module__Up_5VW__className\",\n  \"variable\": \"prompt_df9adab7-module__Up_5VW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/prompt_df9adab7.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Prompt%22,%22arguments%22:[{%22weight%22:[%22400%22,%22600%22,%22800%22],%22subsets%22:[%22latin%22],%22variable%22:%22--font-prompt%22}],%22variableName%22:%22prompt%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Prompt', 'Prompt Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,iKAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,iKAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,iKAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/Navigation.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navigation.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navigation.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/Navigation.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navigation.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navigation.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport { Inter, Prompt } from \"next/font/google\";\nimport \"./globals.css\";\nimport Navigation from \"@/components/Navigation\";\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-inter\",\n});\n\nconst prompt = Prompt({\n  weight: [\"400\", \"600\", \"800\"],\n  subsets: [\"latin\"],\n  variable: \"--font-prompt\",\n});\n\nexport const metadata: Metadata = {\n  title: \"Chocolate & Art Show — Dallas\",\n  description: \"Immersive art, live music, body painting, and artisan chocolate. Two nights only in Dallas. 21+.\",\n  keywords: [\"art show\", \"chocolate\", \"music\", \"Dallas\", \"live art\", \"body painting\", \"nightlife\"],\n  openGraph: {\n    title: \"Chocolate & Art Show — Dallas\",\n    description: \"Immersive art, live music, body painting, and artisan chocolate. Two nights only in Dallas. 21+.\",\n    type: \"website\",\n    locale: \"en_US\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"Chocolate & Art Show — Dallas\",\n    description: \"Immersive art, live music, body painting, and artisan chocolate. Two nights only in Dallas. 21+.\",\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <head>\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"anonymous\" />\n      </head>\n      <body className={`${inter.variable} ${prompt.variable} antialiased`}>\n        <Navigation />\n        <main>{children}</main>\n        <script\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={{\n            __html: JSON.stringify({\n              \"@context\": \"https://schema.org\",\n              \"@type\": \"Event\",\n              name: \"Chocolate and Art Show — Dallas\",\n              startDate: \"2025-09-18T19:00:00-05:00\",\n              endDate: \"2025-09-19T23:59:00-05:00\",\n              eventAttendanceMode: \"https://schema.org/OfflineEventAttendanceMode\",\n              eventStatus: \"https://schema.org/EventScheduled\",\n              location: {\n                \"@type\": \"Place\",\n                name: \"Lofty Spaces\",\n                address: {\n                  \"@type\": \"PostalAddress\",\n                  addressLocality: \"Dallas\",\n                  addressRegion: \"TX\",\n                  addressCountry: \"US\",\n                },\n              },\n              description: \"Immersive art, live music, body painting, and artisan chocolate. 21+.\",\n              offers: [\n                {\n                  \"@type\": \"Offer\",\n                  url: \"https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089\",\n                  priceCurrency: \"USD\",\n                  availability: \"https://schema.org/InStock\",\n                  validFrom: \"2025-08-01T08:00:00-05:00\",\n                },\n              ],\n              performer: { \"@type\": \"PerformingGroup\", name: \"Local Artists & DJs\" },\n              organizer: {\n                \"@type\": \"Organization\",\n                name: \"Chocolate and Art Show\",\n                url: \"https://chocolateandartshow.com\",\n              },\n            }),\n          }}\n        />\n        <script\n          dangerouslySetInnerHTML={{\n            __html: `\n              // Randomize artists header background on each refresh\n              if (typeof window !== 'undefined') {\n                document.documentElement.style.setProperty('--h', Math.floor(Math.random() * 360));\n                document.documentElement.style.setProperty('--a', Math.floor(Math.random() * 360) + 'deg');\n              }\n            `,\n          }}\n        />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAGA;;;;;;AAaO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAY;QAAa;QAAS;QAAU;QAAY;QAAiB;KAAY;IAChG,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;0BACC,cAAA,8OAAC;oBAAK,KAAI;oBAAa,MAAK;oBAA4B,aAAY;;;;;;;;;;;0BAEtE,8OAAC;gBAAK,WAAW,GAAG,oJAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,qJAAM,CAAC,QAAQ,CAAC,YAAY,CAAC;;kCACjE,8OAAC,2IAAU;;;;;kCACX,8OAAC;kCAAM;;;;;;kCACP,8OAAC;wBACC,MAAK;wBACL,yBAAyB;4BACvB,QAAQ,KAAK,SAAS,CAAC;gCACrB,YAAY;gCACZ,SAAS;gCACT,MAAM;gCACN,WAAW;gCACX,SAAS;gCACT,qBAAqB;gCACrB,aAAa;gCACb,UAAU;oCACR,SAAS;oCACT,MAAM;oCACN,SAAS;wCACP,SAAS;wCACT,iBAAiB;wCACjB,eAAe;wCACf,gBAAgB;oCAClB;gCACF;gCACA,aAAa;gCACb,QAAQ;oCACN;wCACE,SAAS;wCACT,KAAK;wCACL,eAAe;wCACf,cAAc;wCACd,WAAW;oCACb;iCACD;gCACD,WAAW;oCAAE,SAAS;oCAAmB,MAAM;gCAAsB;gCACrE,WAAW;oCACT,SAAS;oCACT,MAAM;oCACN,KAAK;gCACP;4BACF;wBACF;;;;;;kCAEF,8OAAC;wBACC,yBAAyB;4BACvB,QAAQ,CAAC;;;;;;YAMT,CAAC;wBACH;;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}