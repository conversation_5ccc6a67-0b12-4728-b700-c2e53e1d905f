{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/app/gallery/page.tsx"], "sourcesContent": ["import Image from 'next/image';\n\nexport const metadata = {\n  title: 'Gallery — Chocolate & Art Show Dallas',\n  description: 'View photos and artwork from previous Chocolate & Art Shows in Dallas.',\n};\n\nconst GalleryPage = () => {\n  // Gallery images from the event\n  const galleryImages = [\n    {\n      id: 1,\n      src: '/assets/gallery/live-art-1.jpg',\n      alt: 'Live painting at Chocolate & Art Show',\n      title: 'Live Art Creation',\n      category: 'Live Art'\n    },\n    {\n      id: 2,\n      src: '/assets/gallery/behind-scenes-1.jpg',\n      alt: 'Artist working on canvas',\n      title: 'Interactive Experience',\n      category: 'Behind the Scenes'\n    },\n    {\n      id: 3,\n      src: '/assets/gallery/chocolate-1.jpg',\n      alt: 'Chocolate tasting station',\n      title: 'Artisan Chocolate',\n      category: 'Food & Drink'\n    },\n    {\n      id: 4,\n      src: '/assets/gallery/music-1.jpg',\n      alt: 'Live music performance',\n      title: 'Live Music',\n      category: 'Music'\n    },\n    {\n      id: 5,\n      src: '/assets/gallery/live-art-2.jpg',\n      alt: 'Body painting art',\n      title: 'Body Art',\n      category: 'Live Art'\n    },\n    {\n      id: 6,\n      src: '/assets/gallery/atmosphere-1.jpg',\n      alt: 'Event atmosphere',\n      title: 'Night Atmosphere',\n      category: 'Atmosphere'\n    },\n    {\n      id: 7,\n      src: '/assets/gallery/atmosphere-2.jpg',\n      alt: 'Crowd enjoying the show',\n      title: 'Engaged Audience',\n      category: 'Atmosphere'\n    },\n    {\n      id: 8,\n      src: '/assets/gallery/artwork-1.jpg',\n      alt: 'Finished artwork display',\n      title: 'Completed Pieces',\n      category: 'Artwork'\n    },\n    {\n      id: 9,\n      src: '/assets/gallery/behind-scenes-2.jpg',\n      alt: 'Artist collaboration',\n      title: 'Artist Collaboration',\n      category: 'Behind the Scenes'\n    }\n  ];\n\n  const categories = ['All', 'Live Art', 'Music', 'Food & Drink', 'Atmosphere', 'Behind the Scenes', 'Artwork'];\n\n  return (\n    <div className=\"pt-16\">\n      {/* Header */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 text-center\">\n        <div className=\"max-w-4xl mx-auto\">\n          <h1 className=\"text-5xl md:text-7xl font-black text-white mb-6\">\n            Gallery\n          </h1>\n          <p className=\"text-xl text-gray-300 mb-8\">\n            Experience the magic through the lens of previous shows\n          </p>\n        </div>\n      </section>\n\n      {/* Category Filter */}\n      <section className=\"px-4 sm:px-6 lg:px-8 mb-12\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"flex flex-wrap justify-center gap-4\">\n            {categories.map((category) => (\n              <button\n                key={category}\n                className=\"px-4 py-2 rounded-lg border border-white/20 text-white hover:bg-white/10 transition-all font-medium\"\n              >\n                {category}\n              </button>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Gallery Grid */}\n      <section className=\"px-4 sm:px-6 lg:px-8 pb-20\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {galleryImages.map((image) => (\n              <div\n                key={image.id}\n                className=\"group relative overflow-hidden rounded-lg bg-gray-800 aspect-[4/3] hover:transform hover:scale-105 transition-all duration-300 cursor-pointer\"\n              >\n                <Image\n                  src={image.src}\n                  alt={image.alt}\n                  fill\n                  className=\"object-cover group-hover:opacity-80 transition-opacity\"\n                  sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity\" />\n                <div className=\"absolute bottom-4 left-4 right-4 transform translate-y-4 group-hover:translate-y-0 transition-transform\">\n                  <span className=\"inline-block bg-purple-600 text-white text-xs px-2 py-1 rounded-full mb-2\">\n                    {image.category}\n                  </span>\n                  <h3 className=\"text-white font-bold text-lg\">{image.title}</h3>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-black/30 text-center\">\n        <div className=\"max-w-4xl mx-auto\">\n          <h2 className=\"text-4xl font-bold text-white mb-6\">\n            Be Part of the Next Show\n          </h2>\n          <p className=\"text-xl text-gray-300 mb-8\">\n            Join us for an unforgettable night of art, music, and chocolate\n          </p>\n          <a\n            href=\"/tickets\"\n            className=\"inline-block bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-lg font-bold text-lg hover:from-blue-600 hover:to-purple-700 transition-all\"\n          >\n            Get Your Tickets\n          </a>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default GalleryPage;\n"], "names": [], "mappings": ";;;;;;;AAAA;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEA,MAAM,cAAc;IAClB,gCAAgC;IAChC,MAAM,gBAAgB;QACpB;YACE,IAAI;YACJ,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;KACD;IAED,MAAM,aAAa;QAAC;QAAO;QAAY;QAAS;QAAgB;QAAc;QAAqB;KAAU;IAE7G,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;0BAO9C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;gCAEC,WAAU;0CAET;+BAHI;;;;;;;;;;;;;;;;;;;;0BAWf,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC,wIAAK;wCACJ,KAAK,MAAM,GAAG;wCACd,KAAK,MAAM,GAAG;wCACd,IAAI;wCACJ,WAAU;wCACV,OAAM;;;;;;kDAER,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DACb,MAAM,QAAQ;;;;;;0DAEjB,8OAAC;gDAAG,WAAU;0DAAgC,MAAM,KAAK;;;;;;;;;;;;;+BAftD,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;0BAwBvB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}]}