{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState } from 'react';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/artists', label: 'Artists' },\n    { href: '/tickets', label: 'Tickets' },\n    { href: '/gallery', label: 'Gallery' },\n    { href: '/schedule', label: 'Schedule' },\n    { href: '/faq', label: 'FAQ' },\n    { href: '/contact', label: 'Contact' },\n  ];\n\n  const eventbriteUrl = \"https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=nav\";\n\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-sm border-b border-white/10\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"text-xl font-bold text-white hover:text-gray-300 transition-colors\">\n            Chocolate & Art\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-white hover:text-gray-300 transition-colors font-medium\"\n              >\n                {item.label}\n              </Link>\n            ))}\n            <a\n              href={eventbriteUrl}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-lg font-bold hover:from-blue-600 hover:to-purple-700 transition-all\"\n            >\n              Get Tickets\n            </a>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setIsOpen(!isOpen)}\n            className=\"md:hidden text-white hover:text-gray-300 transition-colors\"\n            aria-label=\"Toggle menu\"\n          >\n            <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              {isOpen ? (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              ) : (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 bg-black/90 rounded-lg mt-2\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-white hover:text-gray-300 transition-colors font-medium\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n              <a\n                href={eventbriteUrl}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"block bg-gradient-to-r from-blue-500 to-purple-600 text-white px-3 py-2 rounded-lg font-bold hover:from-blue-600 hover:to-purple-700 transition-all text-center mx-3 mt-4\"\n                onClick={() => setIsOpen(false)}\n              >\n                Get Tickets\n              </a>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,aAAa;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAC;IAErC,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAa,OAAO;QAAW;QACvC;YAAE,MAAM;YAAQ,OAAO;QAAM;QAC7B;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,MAAM,gBAAgB;IAEtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,uKAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAqE;;;;;;sCAK9F,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,uKAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,KAAK;uCAJN,KAAK,IAAI;;;;;8CAOlB,8OAAC;oCACC,MAAM;oCACN,QAAO;oCACP,KAAI;oCACJ,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BACC,SAAS,IAAM,UAAU,CAAC;4BAC1B,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC7D,uBACC,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;6FAErE,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAO5E,wBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,uKAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,UAAU;8CAExB,KAAK,KAAK;mCALN,KAAK,IAAI;;;;;0CAQlB,8OAAC;gCACC,MAAM;gCACN,QAAO;gCACP,KAAI;gCACJ,WAAU;gCACV,SAAS,IAAM,UAAU;0CAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}]}