{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/NightSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nconst NightSelector = () => {\n  const [selectedNight, setSelectedNight] = useState<string | null>(null);\n\n  const nights = [\n    {\n      id: 'thu',\n      date: 'Thu • Sept 18',\n      status: 'low',\n      eventbriteUrl: 'https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=night_thu'\n    },\n    {\n      id: 'fri',\n      date: 'Fri • Sept 19',\n      status: 'available',\n      eventbriteUrl: 'https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=night_fri'\n    }\n  ];\n\n  const handleNightClick = (night: typeof nights[0]) => {\n    setSelectedNight(night.id);\n    // Announce to screen readers\n    const announcement = night.status === 'low' \n      ? `${night.date} is almost sold out.` \n      : `${night.date} selected.`;\n    \n    // Create a temporary element for screen reader announcement\n    const announcer = document.createElement('div');\n    announcer.setAttribute('aria-live', 'polite');\n    announcer.setAttribute('aria-atomic', 'true');\n    announcer.className = 'sr-only';\n    announcer.textContent = announcement;\n    document.body.appendChild(announcer);\n    \n    setTimeout(() => {\n      document.body.removeChild(announcer);\n    }, 1000);\n\n    // Open Eventbrite\n    window.open(night.eventbriteUrl, '_blank', 'noopener');\n  };\n\n  return (\n    <section className=\"night-select\" aria-label=\"Choose your night\">\n      <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n        {nights.map((night) => (\n          <button\n            key={night.id}\n            className={`night ${selectedNight === night.id ? 'selected' : ''}`}\n            onClick={() => handleNightClick(night)}\n            aria-describedby={night.status === 'low' ? `${night.id}-status` : undefined}\n          >\n            {night.date}\n            {night.status === 'low' && (\n              <span className=\"tag\" aria-hidden=\"true\">\n                Almost&nbsp;Gone\n              </span>\n            )}\n          </button>\n        ))}\n      </div>\n      \n      {/* Screen reader announcements */}\n      <div className=\"sr-only\" aria-live=\"polite\" aria-atomic=\"true\"></div>\n      \n      {/* Hidden status descriptions for screen readers */}\n      {nights.map((night) => (\n        night.status === 'low' && (\n          <div key={`${night.id}-status`} id={`${night.id}-status`} className=\"sr-only\">\n            Almost sold out\n          </div>\n        )\n      ))}\n    </section>\n  );\n};\n\nexport default NightSelector;\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAIA,MAAM,gBAAgB;IACpB,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAgB;IAElE,MAAM,SAAS;QACb;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,eAAe;QACjB;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,eAAe;QACjB;KACD;IAED,MAAM,mBAAmB,CAAC;QACxB,iBAAiB,MAAM,EAAE;QACzB,6BAA6B;QAC7B,MAAM,eAAe,MAAM,MAAM,KAAK,QAClC,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,GACnC,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC;QAE7B,4DAA4D;QAC5D,MAAM,YAAY,SAAS,aAAa,CAAC;QACzC,UAAU,YAAY,CAAC,aAAa;QACpC,UAAU,YAAY,CAAC,eAAe;QACtC,UAAU,SAAS,GAAG;QACtB,UAAU,WAAW,GAAG;QACxB,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,WAAW;YACT,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B,GAAG;QAEH,kBAAkB;QAClB,OAAO,IAAI,CAAC,MAAM,aAAa,EAAE,UAAU;IAC7C;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAAe,cAAW;;0BAC3C,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;wBAEC,WAAW,CAAC,MAAM,EAAE,kBAAkB,MAAM,EAAE,GAAG,aAAa,IAAI;wBAClE,SAAS,IAAM,iBAAiB;wBAChC,oBAAkB,MAAM,MAAM,KAAK,QAAQ,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,GAAG;;4BAEjE,MAAM,IAAI;4BACV,MAAM,MAAM,KAAK,uBAChB,8OAAC;gCAAK,WAAU;gCAAM,eAAY;0CAAO;;;;;;;uBAPtC,MAAM,EAAE;;;;;;;;;;0BAgBnB,8OAAC;gBAAI,WAAU;gBAAU,aAAU;gBAAS,eAAY;;;;;;YAGvD,OAAO,GAAG,CAAC,CAAC,QACX,MAAM,MAAM,KAAK,uBACf,8OAAC;oBAA+B,IAAI,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC;oBAAE,WAAU;8BAAU;mBAApE,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC;;;;;;;;;;;AAOxC;uCAEe", "debugId": null}}]}