{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/CTABox.tsx"], "sourcesContent": ["const CTABox = () => {\n  const eventbriteUrl = \"https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=ticketsbox\";\n\n  return (\n    <a \n      className=\"cta-box\" \n      href={eventbriteUrl}\n      target=\"_blank\" \n      rel=\"noopener noreferrer\"\n    >\n      <span className=\"cta-title\">Buy Dallas Tickets</span>\n      <span className=\"cta-sub\">Two nights • 21+ • Lofty Spaces</span>\n    </a>\n  );\n};\n\nexport default CTABox;\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,SAAS;IACb,MAAM,gBAAgB;IAEtB,qBACE,8OAAC;QACC,WAAU;QACV,MAAM;QACN,QAAO;QACP,KAAI;;0BAEJ,8OAAC;gBAAK,WAAU;0BAAY;;;;;;0BAC5B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;uCAEe", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/NightSelector.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/NightSelector.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/NightSelector.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/NightSelector.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/NightSelector.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/NightSelector.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/PricingGrid.tsx"], "sourcesContent": ["const PricingGrid = () => {\n  const ticketTypes = [\n    {\n      name: 'General Admission',\n      price: '$35',\n      originalPrice: null,\n      description: 'Access to all art experiences, chocolate tastings, and live music',\n      features: [\n        'Full event access',\n        'Chocolate tastings',\n        'Live art viewing',\n        'Music performances',\n        'Interactive experiences'\n      ],\n      popular: false,\n      eventbriteUrl: 'https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=pricing_general'\n    },\n    {\n      name: 'VIP Experience',\n      price: '$65',\n      originalPrice: '$75',\n      description: 'Premium access with exclusive perks and priority entry',\n      features: [\n        'Everything in General',\n        'Priority entry',\n        'VIP chocolate selection',\n        'Meet the artists',\n        'Complimentary drink',\n        'VIP seating area'\n      ],\n      popular: true,\n      eventbriteUrl: 'https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=pricing_vip'\n    },\n    {\n      name: 'Artist Pass',\n      price: '$25',\n      originalPrice: null,\n      description: 'Special pricing for fellow artists and creatives',\n      features: [\n        'Full event access',\n        'Chocolate tastings',\n        'Artist networking',\n        'Portfolio review opportunity',\n        'Valid art student ID required'\n      ],\n      popular: false,\n      eventbriteUrl: 'https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=pricing_artist'\n    }\n  ];\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n      {ticketTypes.map((ticket, index) => (\n        <div \n          key={index}\n          className={`relative bg-white/5 border rounded-2xl p-6 ${\n            ticket.popular \n              ? 'border-purple-400 ring-2 ring-purple-400/20' \n              : 'border-white/10'\n          }`}\n        >\n          {ticket.popular && (\n            <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n              <span className=\"bg-purple-500 text-white px-4 py-1 rounded-full text-sm font-bold\">\n                Most Popular\n              </span>\n            </div>\n          )}\n          \n          <div className=\"text-center mb-6\">\n            <h3 className=\"text-2xl font-bold text-white mb-2\">{ticket.name}</h3>\n            <div className=\"flex items-center justify-center gap-2 mb-2\">\n              <span className=\"text-4xl font-black text-white\">{ticket.price}</span>\n              {ticket.originalPrice && (\n                <span className=\"text-lg text-gray-400 line-through\">{ticket.originalPrice}</span>\n              )}\n            </div>\n            <p className=\"text-gray-300 text-sm\">{ticket.description}</p>\n          </div>\n\n          <ul className=\"space-y-3 mb-8\">\n            {ticket.features.map((feature, featureIndex) => (\n              <li key={featureIndex} className=\"flex items-center text-gray-200\">\n                <svg className=\"w-5 h-5 text-green-400 mr-3 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                </svg>\n                {feature}\n              </li>\n            ))}\n          </ul>\n\n          <a\n            href={ticket.eventbriteUrl}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className={`block w-full text-center py-3 px-6 rounded-lg font-bold transition-all ${\n              ticket.popular\n                ? 'bg-purple-600 text-white hover:bg-purple-700'\n                : 'bg-white/10 text-white border border-white/20 hover:bg-white/20'\n            }`}\n          >\n            Select {ticket.name}\n          </a>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default PricingGrid;\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,cAAc;IAClB,MAAM,cAAc;QAClB;YACE,MAAM;YACN,OAAO;YACP,eAAe;YACf,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;YACT,eAAe;QACjB;QACA;YACE,MAAM;YACN,OAAO;YACP,eAAe;YACf,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;YACT,eAAe;QACjB;QACA;YACE,MAAM;YACN,OAAO;YACP,eAAe;YACf,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;YACT,eAAe;QACjB;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC;gBAEC,WAAW,CAAC,2CAA2C,EACrD,OAAO,OAAO,GACV,gDACA,mBACJ;;oBAED,OAAO,OAAO,kBACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAoE;;;;;;;;;;;kCAMxF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC,OAAO,IAAI;;;;;;0CAC/D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAkC,OAAO,KAAK;;;;;;oCAC7D,OAAO,aAAa,kBACnB,8OAAC;wCAAK,WAAU;kDAAsC,OAAO,aAAa;;;;;;;;;;;;0CAG9E,8OAAC;gCAAE,WAAU;0CAAyB,OAAO,WAAW;;;;;;;;;;;;kCAG1D,8OAAC;wBAAG,WAAU;kCACX,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC7B,8OAAC;gCAAsB,WAAU;;kDAC/B,8OAAC;wCAAI,WAAU;wCAA4C,MAAK;wCAAe,SAAQ;kDACrF,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAqH,UAAS;;;;;;;;;;;oCAE1J;;+BAJM;;;;;;;;;;kCASb,8OAAC;wBACC,MAAM,OAAO,aAAa;wBAC1B,QAAO;wBACP,KAAI;wBACJ,WAAW,CAAC,uEAAuE,EACjF,OAAO,OAAO,GACV,iDACA,mEACJ;;4BACH;4BACS,OAAO,IAAI;;;;;;;;eA/ChB;;;;;;;;;;AAqDf;uCAEe", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/UrgencyStrip.tsx"], "sourcesContent": ["const UrgencyStrip = () => {\n  return (\n    <div className=\"bg-red-900/30 border-y border-red-500/30 py-3\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <p className=\"text-red-200 font-bold\">\n            ⚠️ Limited Capacity Event • Thursday Almost Sold Out • Friday Filling Fast\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default UrgencyStrip;\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,eAAe;IACnB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;;;;;;;;;;AAOhD;uCAEe", "debugId": null}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/app/tickets/page.tsx"], "sourcesContent": ["import CTABox from '@/components/CTABox';\nimport NightSelector from '@/components/NightSelector';\nimport PricingGrid from '@/components/PricingGrid';\nimport UrgencyStrip from '@/components/UrgencyStrip';\n\nexport const metadata = {\n  title: 'Tickets — Chocolate & Art Show Dallas',\n  description: 'Get your tickets for the Chocolate & Art Show in Dallas. Two nights only - September 18-19, 2025.',\n};\n\nconst TicketsPage = () => {\n  return (\n    <div className=\"pt-16\">\n      {/* Hero Section */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 text-center\">\n        <div className=\"max-w-4xl mx-auto\">\n          <h1 className=\"cta-gradient mb-6\">Buy Dallas Tickets</h1>\n          <p className=\"text-xl text-gray-300 mb-8\">\n            Two nights of immersive art, live music, and artisan chocolate\n          </p>\n          <CTABox />\n        </div>\n      </section>\n\n      {/* Urgency Strip */}\n      <UrgencyStrip />\n\n      {/* Night Selector */}\n      <section className=\"py-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-4xl mx-auto\">\n          <h2 className=\"text-3xl font-bold text-white text-center mb-8\">\n            Choose Your Night\n          </h2>\n          <NightSelector />\n        </div>\n      </section>\n\n      {/* Pricing Grid */}\n      <section className=\"py-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-6xl mx-auto\">\n          <h2 className=\"text-3xl font-bold text-white text-center mb-12\">\n            Ticket Options\n          </h2>\n          <PricingGrid />\n        </div>\n      </section>\n\n      {/* Event Details */}\n      <section className=\"py-12 px-4 sm:px-6 lg:px-8 bg-black/30\">\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"plaque text-center mb-8\">\n            21+ • Last entry 12:30 AM • ID required\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 text-gray-300\">\n            <div>\n              <h3 className=\"text-xl font-bold text-white mb-4\">What's Included</h3>\n              <ul className=\"space-y-2\">\n                <li>• Access to live art creation</li>\n                <li>• Artisan chocolate tastings</li>\n                <li>• Live music performances</li>\n                <li>• Interactive art experiences</li>\n                <li>• Body painting demonstrations</li>\n                <li>• Cash bar available</li>\n              </ul>\n            </div>\n            \n            <div>\n              <h3 className=\"text-xl font-bold text-white mb-4\">Important Info</h3>\n              <ul className=\"space-y-2\">\n                <li>• Doors open at 7:00 PM</li>\n                <li>• Last entry at 12:30 AM</li>\n                <li>• Valid ID required (21+)</li>\n                <li>• No outside food or drinks</li>\n                <li>• Photography encouraged</li>\n                <li>• Dress code: Creative casual</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Final CTA */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 text-center\">\n        <div className=\"max-w-4xl mx-auto\">\n          <h2 className=\"text-4xl font-bold text-white mb-6\">\n            Don't Miss Out\n          </h2>\n          <p className=\"text-xl text-gray-300 mb-8\">\n            Limited capacity • Almost sold out • Two nights only\n          </p>\n          <CTABox />\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default TicketsPage;\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEA,MAAM,cAAc;IAClB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoB;;;;;;sCAClC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC,uIAAM;;;;;;;;;;;;;;;;0BAKX,8OAAC,6IAAY;;;;;0BAGb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,8OAAC,8IAAa;;;;;;;;;;;;;;;;0BAKlB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,8OAAC,4IAAW;;;;;;;;;;;;;;;;0BAKhB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAA0B;;;;;;sCAIzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAIR,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQd,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC,uIAAM;;;;;;;;;;;;;;;;;;;;;;AAKjB;uCAEe", "debugId": null}}]}