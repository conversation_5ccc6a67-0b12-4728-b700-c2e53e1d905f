{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/ArtistGrid.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport Image from 'next/image';\n\nconst ArtistGrid = () => {\n  const artists = [\n    {\n      id: 'da<PERSON>',\n      name: '<PERSON><PERSON>',\n      image: '/api/placeholder/640/420',\n      alt: '<PERSON><PERSON> — portrait',\n      blobClass: 'blob-1'\n    },\n    {\n      id: 'david-v',\n      name: '<PERSON>',\n      image: '/api/placeholder/640/420',\n      alt: '<PERSON> — portrait with hat',\n      blobClass: 'blob-2'\n    },\n    {\n      id: 'omi',\n      name: '<PERSON><PERSON>',\n      image: '/api/placeholder/640/420',\n      alt: '<PERSON><PERSON> — profile with sunglasses',\n      blobClass: 'blob-3'\n    },\n    {\n      id: 'sofia',\n      name: '<PERSON><PERSON><PERSON>',\n      image: '/api/placeholder/640/420',\n      alt: '<PERSON><PERSON><PERSON> — portrait with hat',\n      blobClass: 'blob-4'\n    },\n    {\n      id: 'leon',\n      name: '<PERSON>',\n      image: '/api/placeholder/640/420',\n      alt: '<PERSON> — studio portrait',\n      blobClass: 'blob-5'\n    }\n  ];\n\n  return (\n    <section className=\"artist-preview\" aria-label=\"Featured artists — Dallas\">\n      {/* SVG clip-path definitions */}\n      <svg className=\"visually-hidden\" aria-hidden=\"true\" width=\"0\" height=\"0\" focusable=\"false\">\n        <defs>\n          <clipPath id=\"blob-1\" clipPathUnits=\"objectBoundingBox\">\n            <path d=\"M0.02,0.42 C0.05,0.22 0.20,0.06 0.40,0.06 L0.88,0.06 C0.96,0.06 0.99,0.16 0.98,0.30 C0.96,0.46 0.86,0.66 0.66,0.70 C0.49,0.74 0.40,0.67 0.27,0.72 C0.15,0.76 0.05,0.68 0.03,0.55 Z\"/>\n          </clipPath>\n          <clipPath id=\"blob-2\" clipPathUnits=\"objectBoundingBox\">\n            <path d=\"M0.03,0.38 C0.09,0.16 0.23,0.06 0.45,0.06 L0.86,0.06 C0.96,0.06 1.00,0.18 0.98,0.34 C0.96,0.50 0.87,0.69 0.68,0.73 C0.50,0.77 0.38,0.68 0.24,0.74 C0.12,0.78 0.01,0.70 0.02,0.55 Z\"/>\n          </clipPath>\n          <clipPath id=\"blob-3\" clipPathUnits=\"objectBoundingBox\">\n            <path d=\"M0.04,0.40 C0.07,0.20 0.22,0.07 0.42,0.07 L0.85,0.07 C0.96,0.07 1.00,0.20 0.98,0.35 C0.95,0.52 0.85,0.70 0.64,0.73 C0.44,0.77 0.34,0.67 0.20,0.73 C0.09,0.77 0.01,0.68 0.02,0.53 Z\"/>\n          </clipPath>\n          <clipPath id=\"blob-4\" clipPathUnits=\"objectBoundingBox\">\n            <path d=\"M0.02,0.36 C0.06,0.17 0.21,0.05 0.39,0.05 L0.86,0.05 C0.96,0.05 1.00,0.17 0.98,0.32 C0.95,0.49 0.85,0.69 0.65,0.73 C0.47,0.76 0.36,0.68 0.22,0.74 C0.10,0.78 0.02,0.70 0.02,0.56 Z\"/>\n          </clipPath>\n          <clipPath id=\"blob-5\" clipPathUnits=\"objectBoundingBox\">\n            <path d=\"M0.03,0.39 C0.08,0.18 0.23,0.06 0.41,0.06 L0.87,0.06 C0.97,0.06 1.00,0.17 0.98,0.33 C0.96,0.49 0.86,0.68 0.67,0.72 C0.49,0.76 0.39,0.68 0.26,0.73 C0.14,0.77 0.04,0.70 0.03,0.56 Z\"/>\n          </clipPath>\n        </defs>\n      </svg>\n\n      <ul className=\"artist-grid\" role=\"list\">\n        {artists.map((artist) => (\n          <li key={artist.id} className=\"artist-card\">\n            <Link \n              className=\"artist-link\" \n              href={`/artists/${artist.id}`} \n              aria-label={`View ${artist.name} — bio and works`}\n            >\n              <figure className={`media ${artist.blobClass}`}>\n                <Image\n                  src={artist.image}\n                  alt={artist.alt}\n                  width={640}\n                  height={420}\n                  loading=\"lazy\"\n                  className=\"w-full h-full object-cover\"\n                />\n              </figure>\n              <span className=\"name\">{artist.name}</span>\n            </Link>\n          </li>\n        ))}\n      </ul>\n    </section>\n  );\n};\n\nexport default ArtistGrid;\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEA,MAAM,aAAa;IACjB,MAAM,UAAU;QACd;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,KAAK;YACL,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,KAAK;YACL,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,KAAK;YACL,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,KAAK;YACL,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,KAAK;YACL,WAAW;QACb;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;QAAiB,cAAW;;0BAE7C,8OAAC;gBAAI,WAAU;gBAAkB,eAAY;gBAAO,OAAM;gBAAI,QAAO;gBAAI,WAAU;0BACjF,cAAA,8OAAC;;sCACC,8OAAC;4BAAS,IAAG;4BAAS,eAAc;sCAClC,cAAA,8OAAC;gCAAK,GAAE;;;;;;;;;;;sCAEV,8OAAC;4BAAS,IAAG;4BAAS,eAAc;sCAClC,cAAA,8OAAC;gCAAK,GAAE;;;;;;;;;;;sCAEV,8OAAC;4BAAS,IAAG;4BAAS,eAAc;sCAClC,cAAA,8OAAC;gCAAK,GAAE;;;;;;;;;;;sCAEV,8OAAC;4BAAS,IAAG;4BAAS,eAAc;sCAClC,cAAA,8OAAC;gCAAK,GAAE;;;;;;;;;;;sCAEV,8OAAC;4BAAS,IAAG;4BAAS,eAAc;sCAClC,cAAA,8OAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAKd,8OAAC;gBAAG,WAAU;gBAAc,MAAK;0BAC9B,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;wBAAmB,WAAU;kCAC5B,cAAA,8OAAC,uKAAI;4BACH,WAAU;4BACV,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE;4BAC7B,cAAY,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC,gBAAgB,CAAC;;8CAEjD,8OAAC;oCAAO,WAAW,CAAC,MAAM,EAAE,OAAO,SAAS,EAAE;8CAC5C,cAAA,8OAAC,wIAAK;wCACJ,KAAK,OAAO,KAAK;wCACjB,KAAK,OAAO,GAAG;wCACf,OAAO;wCACP,QAAQ;wCACR,SAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,8OAAC;oCAAK,WAAU;8CAAQ,OAAO,IAAI;;;;;;;;;;;;uBAhB9B,OAAO,EAAE;;;;;;;;;;;;;;;;AAuB5B;uCAEe", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/ApplyCTA.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ApplyCTA.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ApplyCTA.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/ApplyCTA.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ApplyCTA.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ApplyCTA.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/app/artists/page.tsx"], "sourcesContent": ["import ArtistGrid from '@/components/ArtistGrid';\nimport ApplyCTA from '@/components/ApplyCTA';\n\nexport const metadata = {\n  title: 'Artists — Chocolate & Art Show Dallas',\n  description: 'Meet the talented local and emerging artists featured at the Chocolate & Art Show in Dallas.',\n};\n\nconst ArtistsPage = () => {\n  return (\n    <div className=\"pt-16\">\n      {/* Artists Header with Trippy Background */}\n      <header className=\"artists-hero\">\n        <h1>Artists</h1>\n      </header>\n\n      {/* Apply CTA */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <ApplyCTA />\n      </div>\n\n      {/* Artist Grid */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <ArtistGrid />\n      </div>\n\n      {/* Kusama Playground Teaser */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <a \n          className=\"kusama-card\" \n          href=\"/play/kusama\" \n          aria-label=\"Open the Kusama dot playground\"\n        >\n          <figure>\n            <img \n              src=\"/api/placeholder/280/160\" \n              alt=\"Preview of Kusama dot playground\" \n              loading=\"lazy\" \n              width=\"280\" \n              height=\"160\"\n              className=\"w-full h-auto\"\n            />\n          </figure>\n          <span>Play with the dots →</span>\n        </a>\n      </div>\n    </div>\n  );\n};\n\nexport default ArtistsPage;\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEA,MAAM,cAAc;IAClB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;8BAAG;;;;;;;;;;;0BAIN,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,yIAAQ;;;;;;;;;;0BAIX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,2IAAU;;;;;;;;;;0BAIb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,cAAW;;sCAEX,8OAAC;sCACC,cAAA,8OAAC;gCACC,KAAI;gCACJ,KAAI;gCACJ,SAAQ;gCACR,OAAM;gCACN,QAAO;gCACP,WAAU;;;;;;;;;;;sCAGd,8OAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAKhB;uCAEe", "debugId": null}}]}