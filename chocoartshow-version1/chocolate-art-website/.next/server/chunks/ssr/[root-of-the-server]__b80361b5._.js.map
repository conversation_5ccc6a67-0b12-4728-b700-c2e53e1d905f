{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/HeroSection.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/HeroSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/HeroSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/HeroSection.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/HeroSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/HeroSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/RGBWall.tsx"], "sourcesContent": ["const RGBWall = () => {\n  const eventbriteUrl = \"https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=rgbwall\";\n\n  return (\n    <section className=\"rgb-wall\" aria-label=\"Intermission banner\">\n      <div className=\"rgb-hero\">\n        <span className=\"rgb-word\" data-txt=\"ART\">ART</span>\n        <span className=\"rgb-plus\">+</span>\n        <span className=\"rgb-word\" data-txt=\"MUSIC\">MUSIC</span>\n        <span className=\"rgb-plus\">+</span>\n        <span className=\"rgb-word\" data-txt=\"CHOCOLATE\">CHOCOLATE</span>\n        <span className=\"rgb-eq\">=</span>\n        <span className=\"rgb-word\" data-txt=\"DALLAS\">DALLAS</span>\n      </div>\n      <a \n        className=\"rgb-cta\" \n        href={eventbriteUrl}\n        target=\"_blank\" \n        rel=\"noopener noreferrer\"\n      >\n        Buy Tickets\n      </a>\n    </section>\n  );\n};\n\nexport default RGBWall;\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,UAAU;IACd,MAAM,gBAAgB;IAEtB,qBACE,8OAAC;QAAQ,WAAU;QAAW,cAAW;;0BACvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;wBAAW,YAAS;kCAAM;;;;;;kCAC1C,8OAAC;wBAAK,WAAU;kCAAW;;;;;;kCAC3B,8OAAC;wBAAK,WAAU;wBAAW,YAAS;kCAAQ;;;;;;kCAC5C,8OAAC;wBAAK,WAAU;kCAAW;;;;;;kCAC3B,8OAAC;wBAAK,WAAU;wBAAW,YAAS;kCAAY;;;;;;kCAChD,8OAAC;wBAAK,WAAU;kCAAS;;;;;;kCACzB,8OAAC;wBAAK,WAAU;wBAAW,YAAS;kCAAS;;;;;;;;;;;;0BAE/C,8OAAC;gBACC,WAAU;gBACV,MAAM;gBACN,QAAO;gBACP,KAAI;0BACL;;;;;;;;;;;;AAKP;uCAEe", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/WhatIsSection.tsx"], "sourcesContent": ["const WhatIsSection = () => {\n  return (\n    <section className=\"py-20 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-12\">\n          {/* EXPLORE Column */}\n          <div className=\"space-y-4\">\n            <h2 className=\"text-4xl md:text-5xl font-black text-white leading-tight\">\n              EXPLORE\n            </h2>\n            <div className=\"space-y-2 text-lg leading-relaxed text-gray-300\">\n              <p>Where art meets indulgence</p>\n              <p>Live creation in real time</p>\n              <p>Interactive experiences</p>\n              <p>Sensory immersion</p>\n              <p>Community connection</p>\n            </div>\n          </div>\n\n          {/* DISCOVER Column */}\n          <div className=\"space-y-6\">\n            <div>\n              <span className=\"text-sm uppercase tracking-wider text-gray-400 font-semibold\">\n                What to Expect\n              </span>\n              <h2 className=\"text-4xl md:text-5xl font-black text-white mt-2\">\n                DISCOVER\n              </h2>\n            </div>\n            \n            <div className=\"space-y-4\">\n              <div className=\"flex space-x-6 text-sm font-semibold\">\n                <button className=\"text-white border-b-2 border-blue-400 pb-1\">Artists</button>\n                <button className=\"text-gray-400 hover:text-white transition-colors\">Music</button>\n                <button className=\"text-gray-400 hover:text-white transition-colors\">Chocolate</button>\n              </div>\n              \n              <div className=\"text-gray-300\">\n                <p className=\"mb-4\">\n                  Watch local and emerging artists create live paintings, sculptures, and digital art \n                  while you enjoy artisan chocolate and craft cocktails.\n                </p>\n                <p>\n                  Each night features different artists, musicians, and chocolate makers, \n                  creating a unique experience every time.\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* IMAGINE Column */}\n          <div className=\"space-y-4\">\n            <h2 className=\"text-4xl md:text-5xl font-black text-white leading-tight\">\n              IMAGINE\n            </h2>\n            <div className=\"bg-gradient-to-br from-purple-900/30 to-blue-900/30 p-6 rounded-lg border border-white/10\">\n              <p className=\"text-lg text-gray-200 italic leading-relaxed\">\n                \"A night where creativity flows as freely as the chocolate, \n                where every brushstroke tells a story, and every bite is an experience.\"\n              </p>\n              <div className=\"mt-4 text-sm text-gray-400\">\n                — What guests are saying\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default WhatIsSection;\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,gBAAgB;IACpB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAE;;;;;;kDACH,8OAAC;kDAAE;;;;;;kDACH,8OAAC;kDAAE;;;;;;kDACH,8OAAC;kDAAE;;;;;;kDACH,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;kCAKP,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;kDAA+D;;;;;;kDAG/E,8OAAC;wCAAG,WAAU;kDAAkD;;;;;;;;;;;;0CAKlE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAO,WAAU;0DAA6C;;;;;;0DAC/D,8OAAC;gDAAO,WAAU;0DAAmD;;;;;;0DACrE,8OAAC;gDAAO,WAAU;0DAAmD;;;;;;;;;;;;kDAGvE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAO;;;;;;0DAIpB,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAST,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA+C;;;;;;kDAI5D,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1D;uCAEe", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/GalleryTeaser.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport Image from 'next/image';\n\nconst GalleryTeaser = () => {\n  // Placeholder images - these would be replaced with actual gallery images\n  const galleryImages = [\n    {\n      id: 1,\n      src: 'public/assets/gallery-teaser/1.jpg',\n      alt: 'Live painting at Chocolate & Art Show',\n      title: 'Live Art Creation'\n    },\n    {\n      id: 2,\n      src: '/api/placeholder/400/300',\n      alt: 'Artist working on canvas',\n      title: 'Interactive Experience'\n    },\n    {\n      id: 3,\n      src: '/api/placeholder/400/300',\n      alt: 'Chocolate tasting station',\n      title: 'Artisan Chocolate'\n    },\n    {\n      id: 4,\n      src: '/api/placeholder/400/300',\n      alt: 'Live music performance',\n      title: 'Live Music'\n    },\n    {\n      id: 5,\n      src: '/api/placeholder/400/300',\n      alt: 'Body painting art',\n      title: 'Body Art'\n    },\n    {\n      id: 6,\n      src: '/api/placeholder/400/300',\n      alt: 'Event atmosphere',\n      title: 'Night Atmosphere'\n    }\n  ];\n\n  return (\n    <section className=\"py-20 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-6xl font-black text-white mb-4\">\n            Experience the Magic\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Get a glimpse of what awaits you at Dallas's most immersive art experience\n          </p>\n        </div>\n\n        {/* Gallery Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12\">\n          {galleryImages.map((image) => (\n            <div\n              key={image.id}\n              className=\"group relative overflow-hidden rounded-lg bg-gray-800 aspect-[4/3] hover:transform hover:scale-105 transition-all duration-300\">\n              <Image\n                src={image.src}\n                alt={image.alt}\n                fill\n                className=\"object-cover group-hover:opacity-80 transition-opacity\"\n                sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n              />\n              <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\" />\n              <div className=\"absolute bottom-4 left-4 right-4\">\n                <h3 className=\"text-white font-bold text-lg\">{image.title}</h3>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* CTA to Gallery */}\n        <div className=\"text-center\">\n          <Link\n            href=\"/gallery\"\n            className=\"inline-block bg-white/10 border border-white/30 text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-white/20 transition-all\"\n          >\n            View Full Gallery\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default GalleryTeaser;\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEA,MAAM,gBAAgB;IACpB,0EAA0E;IAC1E,MAAM,gBAAgB;QACpB;YACE,IAAI;YACJ,KAAK;YACL,KAAK;YACL,OAAO;QACT;QACA;YACE,IAAI;YACJ,KAAK;YACL,KAAK;YACL,OAAO;QACT;QACA;YACE,IAAI;YACJ,KAAK;YACL,KAAK;YACL,OAAO;QACT;QACA;YACE,IAAI;YACJ,KAAK;YACL,KAAK;YACL,OAAO;QACT;QACA;YACE,IAAI;YACJ,KAAK;YACL,KAAK;YACL,OAAO;QACT;QACA;YACE,IAAI;YACJ,KAAK;YACL,KAAK;YACL,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,8OAAC;4BAEC,WAAU;;8CACV,8OAAC,wIAAK;oCACJ,KAAK,MAAM,GAAG;oCACd,KAAK,MAAM,GAAG;oCACd,IAAI;oCACJ,WAAU;oCACV,OAAM;;;;;;8CAER,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDAAgC,MAAM,KAAK;;;;;;;;;;;;2BAXtD,MAAM,EAAE;;;;;;;;;;8BAkBnB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,uKAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/SocialProof.tsx"], "sourcesContent": ["const SocialProof = () => {\n  const testimonials = [\n    {\n      id: 1,\n      text: \"An incredible night of art, music, and chocolate. Unlike anything I've experienced in Dallas!\",\n      author: \"<PERSON>\",\n      event: \"Previous Show Attendee\"\n    },\n    {\n      id: 2,\n      text: \"The live art creation while enjoying artisan chocolate was absolutely magical.\",\n      author: \"<PERSON>\",\n      event: \"Art Enthusiast\"\n    },\n    {\n      id: 3,\n      text: \"Perfect date night! The atmosphere was electric and the artists were so talented.\",\n      author: \"<PERSON> & Tom\",\n      event: \"<PERSON><PERSON><PERSON>'s Night Out\"\n    }\n  ];\n\n  const stats = [\n    { number: \"15\", label: \"Years Running\" },\n    { number: \"500+\", label: \"Artists Featured\" },\n    { number: \"10K+\", label: \"Happy Guests\" },\n    { number: \"2\", label: \"Nights Only\" }\n  ];\n\n  return (\n    <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-black/30\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Stats Section */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mb-16\">\n          {stats.map((stat, index) => (\n            <div key={index} className=\"text-center\">\n              <div className=\"text-4xl md:text-5xl font-black text-white mb-2\">\n                {stat.number}\n              </div>\n              <div className=\"text-gray-400 font-semibold\">\n                {stat.label}\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Testimonials */}\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-black text-white mb-12\">\n            What People Are Saying\n          </h2>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {testimonials.map((testimonial) => (\n              <div key={testimonial.id} className=\"bg-white/5 border border-white/10 rounded-lg p-6 backdrop-blur-sm\">\n                <p className=\"text-gray-200 italic mb-4 text-lg leading-relaxed\">\n                  {testimonial.text}\n                </p>\n                <div className=\"text-sm\">\n                  <div className=\"text-white font-semibold\">{testimonial.author}</div>\n                  <div className=\"text-gray-400\">{testimonial.event}</div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* iMessage Style FOMO Chat */}\n        <div className=\"max-w-2xl mx-auto\">\n          <div className=\"bg-gray-900/50 rounded-2xl p-6 border border-white/10\">\n            <div className=\"space-y-4\">\n              <div className=\"flex justify-start\">\n                <div className=\"bg-gray-300 text-black rounded-2xl rounded-bl-md px-4 py-2 max-w-xs\">\n                  Thu — The Dallas Stones are playing.\n                </div>\n              </div>\n\n              <div className=\"flex justify-end\">\n                <div className=\"bg-blue-500 text-white rounded-2xl rounded-br-md px-4 py-2 max-w-xs\">\n                  Friday is DJ Omu.\n                </div>\n              </div>\n\n              <div className=\"flex justify-start\">\n                <div className=\"bg-gray-300 text-black rounded-2xl rounded-bl-md px-4 py-2 max-w-xs\">\n                  Then we better go both nights.\n                </div>\n              </div>\n\n              <div className=\"flex justify-end\">\n                <div className=\"bg-blue-500 text-white rounded-2xl rounded-br-md px-4 py-2 max-w-xs\">\n                  Hope we can get in — it&apos;s almost sold out.\n                  <a\n                    href=\"https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=imessage\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"underline font-bold ml-1\"\n                  >\n                    Grab tickets\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default SocialProof;\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,cAAc;IAClB,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,OAAO;QACT;KACD;IAED,MAAM,QAAQ;QACZ;YAAE,QAAQ;YAAM,OAAO;QAAgB;QACvC;YAAE,QAAQ;YAAQ,OAAO;QAAmB;QAC5C;YAAE,QAAQ;YAAQ,OAAO;QAAe;QACxC;YAAE,QAAQ;YAAK,OAAO;QAAc;KACrC;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;8CACZ,KAAK,MAAM;;;;;;8CAEd,8OAAC;oCAAI,WAAU;8CACZ,KAAK,KAAK;;;;;;;2BALL;;;;;;;;;;8BAYd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAIjE,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,8OAAC;oCAAyB,WAAU;;sDAClC,8OAAC;4CAAE,WAAU;sDACV,YAAY,IAAI;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA4B,YAAY,MAAM;;;;;;8DAC7D,8OAAC;oDAAI,WAAU;8DAAiB,YAAY,KAAK;;;;;;;;;;;;;mCAN3C,YAAY,EAAE;;;;;;;;;;;;;;;;8BAc9B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAsE;;;;;;;;;;;8CAKvF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAsE;;;;;;;;;;;8CAKvF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAsE;;;;;;;;;;;8CAKvF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;4CAAsE;0DAEnF,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;uCAEe", "debugId": null}}, {"offset": {"line": 816, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/app/page.tsx"], "sourcesContent": ["import HeroSection from \"@/components/HeroSection\";\nimport RGB<PERSON>all from \"@/components/RGBWall\";\nimport WhatIsSection from \"@/components/WhatIsSection\";\nimport GalleryTeaser from \"@/components/GalleryTeaser\";\nimport SocialProof from \"@/components/SocialProof\";\n\nexport default function Home() {\n  return (\n    <div className=\"pt-16\">\n      <HeroSection />\n      <WhatIsSection />\n      <RGBWall />\n      <GalleryTeaser />\n      <SocialProof />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4IAAW;;;;;0BACZ,8OAAC,8IAAa;;;;;0BACd,8OAAC,wIAAO;;;;;0BACR,8OAAC,8IAAa;;;;;0BACd,8OAAC,4IAAW;;;;;;;;;;;AAGlB", "debugId": null}}]}