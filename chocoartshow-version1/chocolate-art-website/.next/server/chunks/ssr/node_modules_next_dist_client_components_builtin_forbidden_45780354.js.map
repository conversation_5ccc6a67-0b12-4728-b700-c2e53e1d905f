{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/node_modules/next/src/client/components/builtin/forbidden.tsx"], "sourcesContent": ["import { HTTPAccessErrorFallback } from '../http-access-fallback/error-fallback'\n\nexport default function Forbidden() {\n  return (\n    <HTTPAccessErrorFallback\n      status={403}\n      message=\"This page could not be accessed.\"\n    />\n  )\n}\n"], "names": ["Forbidden", "HTTPAccessErrorFallback", "status", "message"], "mappings": ";;;+BAEA,WAAA;;;eAAwBA;;;;+BAFgB;AAEzB,SAASA;IACtB,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACC,eAAAA,uBAAuB,EAAA;QACtBC,QAAQ;QACRC,SAAQ;;AAGd", "ignoreList": [0], "debugId": null}}]}