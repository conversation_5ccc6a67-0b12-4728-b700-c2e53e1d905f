{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/app/schedule/page.tsx"], "sourcesContent": ["export const metadata = {\n  title: 'Schedule — Chocolate & Art Show Dallas',\n  description: 'View the schedule for both nights of the Chocolate & Art Show in Dallas.',\n};\n\nconst SchedulePage = () => {\n  const thursdaySchedule = [\n    { time: '7:00 PM', event: 'Doors Open', description: 'Welcome reception with chocolate tastings' },\n    { time: '7:30 PM', event: 'Live Art Begins', description: 'Artists start creating live works' },\n    { time: '8:00 PM', event: 'The Dallas Stones', description: 'Live music performance' },\n    { time: '9:00 PM', event: 'Body Painting Demo', description: 'Interactive body art demonstration' },\n    { time: '10:00 PM', event: 'Artist Spotlight', description: 'Featured artist presentation' },\n    { time: '11:00 PM', event: 'Chocolate Pairing', description: 'Guided chocolate and wine pairing' },\n    { time: '12:00 AM', event: 'Late Night Art', description: 'Continued live art creation' },\n    { time: '12:30 AM', event: 'Last Entry', description: 'Final opportunity to enter' },\n    { time: '2:00 AM', event: 'Event Ends', description: 'Closing and artwork viewing' }\n  ];\n\n  const fridaySchedule = [\n    { time: '7:00 PM', event: 'Doors Open', description: 'Welcome reception with chocolate tastings' },\n    { time: '7:30 PM', event: 'Live Art Begins', description: 'Artists start creating live works' },\n    { time: '8:00 PM', event: 'DJ Omu', description: 'Electronic music sets' },\n    { time: '9:00 PM', event: 'Interactive Art Station', description: 'Guests create alongside artists' },\n    { time: '10:00 PM', event: 'Chocolate Making Demo', description: 'Learn from master chocolatiers' },\n    { time: '11:00 PM', event: 'Live Collaboration', description: 'Artists collaborate on large piece' },\n    { time: '12:00 AM', event: 'DJ Omu Returns', description: 'Late night music continues' },\n    { time: '12:30 AM', event: 'Last Entry', description: 'Final opportunity to enter' },\n    { time: '2:00 AM', event: 'Event Ends', description: 'Closing and artwork viewing' }\n  ];\n\n  return (\n    <div className=\"pt-16\">\n      {/* Header */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 text-center\">\n        <div className=\"max-w-4xl mx-auto\">\n          <h1 className=\"text-5xl md:text-7xl font-black text-white mb-6\">\n            Schedule\n          </h1>\n          <p className=\"text-xl text-gray-300 mb-8\">\n            Two unique nights of art, music, and chocolate\n          </p>\n        </div>\n      </section>\n\n      {/* Schedule Grid */}\n      <section className=\"px-4 sm:px-6 lg:px-8 pb-20\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n            \n            {/* Thursday Schedule */}\n            <div>\n              <div className=\"text-center mb-8\">\n                <h2 className=\"text-4xl font-bold text-white mb-2\">Thursday, Sept 18</h2>\n                <p className=\"text-gray-300\">Featuring The Dallas Stones</p>\n                <span className=\"inline-block bg-yellow-500 text-black px-3 py-1 rounded-full text-sm font-bold mt-2\">\n                  Almost Sold Out\n                </span>\n              </div>\n              \n              <div className=\"space-y-4\">\n                {thursdaySchedule.map((item, index) => (\n                  <div key={index} className=\"bg-white/5 border border-white/10 rounded-lg p-4\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center gap-4 mb-2\">\n                          <span className=\"text-blue-400 font-bold text-lg min-w-[80px]\">\n                            {item.time}\n                          </span>\n                          <h3 className=\"text-white font-bold text-lg\">\n                            {item.event}\n                          </h3>\n                        </div>\n                        <p className=\"text-gray-300 ml-[96px]\">\n                          {item.description}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Friday Schedule */}\n            <div>\n              <div className=\"text-center mb-8\">\n                <h2 className=\"text-4xl font-bold text-white mb-2\">Friday, Sept 19</h2>\n                <p className=\"text-gray-300\">Featuring DJ Omu</p>\n                <span className=\"inline-block bg-green-500 text-black px-3 py-1 rounded-full text-sm font-bold mt-2\">\n                  Tickets Available\n                </span>\n              </div>\n              \n              <div className=\"space-y-4\">\n                {fridaySchedule.map((item, index) => (\n                  <div key={index} className=\"bg-white/5 border border-white/10 rounded-lg p-4\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center gap-4 mb-2\">\n                          <span className=\"text-purple-400 font-bold text-lg min-w-[80px]\">\n                            {item.time}\n                          </span>\n                          <h3 className=\"text-white font-bold text-lg\">\n                            {item.event}\n                          </h3>\n                        </div>\n                        <p className=\"text-gray-300 ml-[96px]\">\n                          {item.description}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n          </div>\n        </div>\n      </section>\n\n      {/* Important Notes */}\n      <section className=\"py-12 px-4 sm:px-6 lg:px-8 bg-black/30\">\n        <div className=\"max-w-4xl mx-auto\">\n          <h2 className=\"text-3xl font-bold text-white text-center mb-8\">Important Notes</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"bg-white/5 border border-white/10 rounded-lg p-6\">\n              <h3 className=\"text-xl font-bold text-white mb-3\">General Information</h3>\n              <ul className=\"text-gray-300 space-y-2\">\n                <li>• Schedule subject to change</li>\n                <li>• All times are approximate</li>\n                <li>• Artists work throughout the evening</li>\n                <li>• Multiple activities happen simultaneously</li>\n              </ul>\n            </div>\n            <div className=\"bg-white/5 border border-white/10 rounded-lg p-6\">\n              <h3 className=\"text-xl font-bold text-white mb-3\">What to Expect</h3>\n              <ul className=\"text-gray-300 space-y-2\">\n                <li>• Continuous chocolate tastings</li>\n                <li>• Interactive art opportunities</li>\n                <li>• Meet and greet with artists</li>\n                <li>• Photo opportunities throughout</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 text-center\">\n        <div className=\"max-w-4xl mx-auto\">\n          <h2 className=\"text-4xl font-bold text-white mb-6\">\n            Choose Your Night\n          </h2>\n          <p className=\"text-xl text-gray-300 mb-8\">\n            Each night offers a unique experience with different artists and performers\n          </p>\n          <a\n            href=\"/tickets\"\n            className=\"inline-block bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-lg font-bold text-lg hover:from-blue-600 hover:to-purple-700 transition-all\"\n          >\n            Get Your Tickets\n          </a>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default SchedulePage;\n"], "names": [], "mappings": ";;;;;;;;AAAO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEA,MAAM,eAAe;IACnB,MAAM,mBAAmB;QACvB;YAAE,MAAM;YAAW,OAAO;YAAc,aAAa;QAA4C;QACjG;YAAE,MAAM;YAAW,OAAO;YAAmB,aAAa;QAAoC;QAC9F;YAAE,MAAM;YAAW,OAAO;YAAqB,aAAa;QAAyB;QACrF;YAAE,MAAM;YAAW,OAAO;YAAsB,aAAa;QAAqC;QAClG;YAAE,MAAM;YAAY,OAAO;YAAoB,aAAa;QAA+B;QAC3F;YAAE,MAAM;YAAY,OAAO;YAAqB,aAAa;QAAoC;QACjG;YAAE,MAAM;YAAY,OAAO;YAAkB,aAAa;QAA8B;QACxF;YAAE,MAAM;YAAY,OAAO;YAAc,aAAa;QAA6B;QACnF;YAAE,MAAM;YAAW,OAAO;YAAc,aAAa;QAA8B;KACpF;IAED,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAW,OAAO;YAAc,aAAa;QAA4C;QACjG;YAAE,MAAM;YAAW,OAAO;YAAmB,aAAa;QAAoC;QAC9F;YAAE,MAAM;YAAW,OAAO;YAAU,aAAa;QAAwB;QACzE;YAAE,MAAM;YAAW,OAAO;YAA2B,aAAa;QAAkC;QACpG;YAAE,MAAM;YAAY,OAAO;YAAyB,aAAa;QAAiC;QAClG;YAAE,MAAM;YAAY,OAAO;YAAsB,aAAa;QAAqC;QACnG;YAAE,MAAM;YAAY,OAAO;YAAkB,aAAa;QAA6B;QACvF;YAAE,MAAM;YAAY,OAAO;YAAc,aAAa;QAA6B;QACnF;YAAE,MAAM;YAAW,OAAO;YAAc,aAAa;QAA8B;KACpF;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;0BAO9C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAGb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAC7B,8OAAC;gDAAK,WAAU;0DAAsF;;;;;;;;;;;;kDAKxG,8OAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,MAAM,sBAC3B,8OAAC;gDAAgB,WAAU;0DACzB,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFACb,KAAK,IAAI;;;;;;kFAEZ,8OAAC;wEAAG,WAAU;kFACX,KAAK,KAAK;;;;;;;;;;;;0EAGf,8OAAC;gEAAE,WAAU;0EACV,KAAK,WAAW;;;;;;;;;;;;;;;;;+CAZf;;;;;;;;;;;;;;;;0CAsBhB,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAC7B,8OAAC;gDAAK,WAAU;0DAAqF;;;;;;;;;;;;kDAKvG,8OAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC;gDAAgB,WAAU;0DACzB,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFACb,KAAK,IAAI;;;;;;kFAEZ,8OAAC;wEAAG,WAAU;kFACX,KAAK,KAAK;;;;;;;;;;;;0EAGf,8OAAC;gEAAE,WAAU;0EACV,KAAK,WAAW;;;;;;;;;;;;;;;;;+CAZf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA0BtB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAC/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAGR,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQd,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}]}