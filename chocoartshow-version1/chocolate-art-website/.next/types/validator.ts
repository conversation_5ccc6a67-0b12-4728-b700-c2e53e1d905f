// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}


// Validate ../../src/app/artists/[slug]/page.tsx
{
  const handler = {} as typeof import("../../src/app/artists/[slug]/page.js")
  handler satisfies AppPageConfig<"/artists/[slug]">
}

// Validate ../../src/app/artists/page.tsx
{
  const handler = {} as typeof import("../../src/app/artists/page.js")
  handler satisfies AppPageConfig<"/artists">
}

// Validate ../../src/app/contact/page.tsx
{
  const handler = {} as typeof import("../../src/app/contact/page.js")
  handler satisfies AppPageConfig<"/contact">
}

// Validate ../../src/app/faq/page.tsx
{
  const handler = {} as typeof import("../../src/app/faq/page.js")
  handler satisfies AppPageConfig<"/faq">
}

// Validate ../../src/app/gallery/page.tsx
{
  const handler = {} as typeof import("../../src/app/gallery/page.js")
  handler satisfies AppPageConfig<"/gallery">
}

// Validate ../../src/app/page.tsx
{
  const handler = {} as typeof import("../../src/app/page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ../../src/app/play/kusama/page.tsx
{
  const handler = {} as typeof import("../../src/app/play/kusama/page.js")
  handler satisfies AppPageConfig<"/play/kusama">
}

// Validate ../../src/app/schedule/page.tsx
{
  const handler = {} as typeof import("../../src/app/schedule/page.js")
  handler satisfies AppPageConfig<"/schedule">
}

// Validate ../../src/app/tickets/page.tsx
{
  const handler = {} as typeof import("../../src/app/tickets/page.js")
  handler satisfies AppPageConfig<"/tickets">
}







// Validate ../../src/app/layout.tsx
{
  const handler = {} as typeof import("../../src/app/layout.js")
  handler satisfies LayoutConfig<"/">
}
