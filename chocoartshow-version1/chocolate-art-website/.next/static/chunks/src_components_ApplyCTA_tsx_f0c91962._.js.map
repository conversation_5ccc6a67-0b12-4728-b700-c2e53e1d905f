{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/ApplyCTA.tsx"], "sourcesContent": ["'use client';\n\nconst ApplyCTA = () => {\n  const createGmailLink = (to: string, subject: string, body: string) => {\n    const encodedSubject = encodeURIComponent(subject);\n    const encodedBody = encodeURIComponent(body);\n    return `https://mail.google.com/mail/?view=cm&fs=1&to=${encodeURIComponent(to)}&su=${encodedSubject}&body=${encodedBody}`;\n  };\n\n  const applications = [\n    {\n      type: 'Artists',\n      email: '<EMAIL>',\n      subject: 'Artist Submission — Dallas Sept 18–19',\n      body: 'Name:\\nInstagram:\\nPortfolio:\\nMedium/Style:\\nNight preference (Thu/Fri/Either):\\nNotes:\\n'\n    },\n    {\n      type: 'Vendors',\n      email: '<EMAIL>',\n      subject: 'Vendor Application — Dallas Sept 18–19',\n      body: 'Business name:\\nProduct category:\\nWebsite/Instagram:\\nPower needs:\\nNight preference:\\nNotes:\\n'\n    },\n    {\n      type: 'Musicians/DJs',\n      email: '<EMAIL>',\n      subject: 'Musician/DJ Application — Dallas Sept 18–19',\n      body: 'Act name:\\nGenre:\\nLinks (YouTube/SoundCloud/Instagram):\\nTech rider (basic):\\nAvailability (Thu/Fri):\\nNotes:\\n'\n    }\n  ];\n\n  return (\n    <section className=\"apply-cta\" aria-label=\"Apply to Chocolate & Art Show — Dallas\">\n      <div className=\"apply-inner\">\n        <h2>Artists • Vendors • Musicians — Apply</h2>\n        <p>Spots are limited. Dallas is almost sold out — email us to be considered.</p>\n        <div className=\"btns\">\n          {applications.map((app, index) => (\n            <a\n              key={index}\n              className=\"btn\"\n              href={createGmailLink(app.email, app.subject, app.body)}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              {app.type} apply\n            </a>\n          ))}\n        </div>\n        <p className=\"microcopy\">\n          Space is extremely limited and nearly sold out. Email now to be considered—complete submissions receive priority.\n        </p>\n      </div>\n    </section>\n  );\n};\n\nexport default ApplyCTA;\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,WAAW;IACf,MAAM,kBAAkB,CAAC,IAAY,SAAiB;QACpD,MAAM,iBAAiB,mBAAmB;QAC1C,MAAM,cAAc,mBAAmB;QACvC,OAAO,AAAC,iDAA6E,OAA7B,mBAAmB,KAAI,QAA6B,OAAvB,gBAAe,UAAoB,OAAZ;IAC9G;IAEA,MAAM,eAAe;QACnB;YACE,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM;QACR;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;QAAY,cAAW;kBACxC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;8BAAG;;;;;;8BACJ,6LAAC;8BAAE;;;;;;8BACH,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,KAAK,sBACtB,6LAAC;4BAEC,WAAU;4BACV,MAAM,gBAAgB,IAAI,KAAK,EAAE,IAAI,OAAO,EAAE,IAAI,IAAI;4BACtD,QAAO;4BACP,KAAI;;gCAEH,IAAI,IAAI;gCAAC;;2BANL;;;;;;;;;;8BAUX,6LAAC;oBAAE,WAAU;8BAAY;;;;;;;;;;;;;;;;;AAMjC;KApDM;uCAsDS", "debugId": null}}]}