{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/components/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\nconst HeroSection = () => {\n  const [isReducedMotion, setIsReducedMotion] = useState(false);\n\n  useEffect(() => {\n    // Check for reduced motion preference\n    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');\n    setIsReducedMotion(mediaQuery.matches);\n\n    const handleChange = (e: MediaQueryListEvent) => {\n      setIsReducedMotion(e.matches);\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, []);\n\n  const eventbriteUrl = \"https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=hero\";\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Video */}\n      <div className=\"absolute inset-0 z-0\">\n        <div className=\"video-bg\">\n          <iframe \n            src=\"https://www.youtube.com/embed/dQw4w9WgXcQ?start=30&autoplay=1&mute=1&loop=1&playlist=dQw4w9WgXcQ&controls=0&rel=0&playsinline=1\" \n            allow=\"autoplay\" \n            frameBorder=\"0\"\n            className=\"absolute top-1/2 left-1/2 w-[177.777vh] min-w-full h-screen transform -translate-x-1/2 -translate-y-1/2\"\n          />\n        </div>\n        <div className=\"absolute inset-0 bg-black/55 z-10\" />\n      </div>\n\n      {/* Hero Content */}\n      <div className=\"relative z-20 text-center px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto\">\n        {/* Waterfall Headline */}\n        <div \n          className={`waterfall mb-8 ${isReducedMotion ? 'transform-none' : ''}`} \n          aria-label=\"Chocolate and Art Show Dallas\"\n        >\n          <span className=\"line\">Chocolate &</span>\n          <span className=\"line\">Art Show</span>\n          <span className=\"line\">Dallas</span>\n        </div>\n\n        {/* Subtitle */}\n        <p className=\"text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto\">\n          Immersive art, live music, body painting, and artisan chocolate. Two nights only in Dallas.\n        </p>\n\n        {/* Date Selector */}\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-8\">\n          <button className=\"night-btn bg-white/10 border border-white/30 text-white px-6 py-3 rounded-lg font-bold hover:bg-white/20 transition-all\">\n            Thu • Sept 18 <span className=\"ml-2 text-xs bg-yellow-400 text-black px-2 py-1 rounded-full\">Almost Gone</span>\n          </button>\n          <button className=\"night-btn bg-white/10 border border-white/30 text-white px-6 py-3 rounded-lg font-bold hover:bg-white/20 transition-all\">\n            Fri • Sept 19\n          </button>\n        </div>\n\n        {/* CTA Gradient Headline */}\n        <h2 className=\"cta-gradient mb-6\">Buy Dallas Tickets</h2>\n\n        {/* Main CTA Button */}\n        <a\n          href={eventbriteUrl}\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          className=\"inline-block bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-lg font-bold text-lg hover:from-blue-600 hover:to-purple-700 transition-all transform hover:scale-105\"\n        >\n          Get Tickets Now\n        </a>\n\n        {/* Event Details */}\n        <div className=\"mt-8 text-gray-300\">\n          <p className=\"text-sm\">21+ • Lofty Spaces • Dallas</p>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20\">\n        <div className=\"animate-bounce\">\n          <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n          </svg>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAIA,MAAM,cAAc;;IAClB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,yKAAQ,EAAC;IAEvD,IAAA,0KAAS;iCAAC;YACR,sCAAsC;YACtC,MAAM,aAAa,OAAO,UAAU,CAAC;YACrC,mBAAmB,WAAW,OAAO;YAErC,MAAM;sDAAe,CAAC;oBACpB,mBAAmB,EAAE,OAAO;gBAC9B;;YAEA,WAAW,gBAAgB,CAAC,UAAU;YACtC;yCAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;gCAAG,EAAE;IAEL,MAAM,gBAAgB;IAEtB,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,KAAI;4BACJ,OAAM;4BACN,aAAY;4BACZ,WAAU;;;;;;;;;;;kCAGd,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,WAAW,AAAC,kBAAyD,OAAxC,kBAAkB,mBAAmB;wBAClE,cAAW;;0CAEX,6LAAC;gCAAK,WAAU;0CAAO;;;;;;0CACvB,6LAAC;gCAAK,WAAU;0CAAO;;;;;;0CACvB,6LAAC;gCAAK,WAAU;0CAAO;;;;;;;;;;;;kCAIzB,6LAAC;wBAAE,WAAU;kCAA2D;;;;;;kCAKxE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;;oCAA0H;kDAC5H,6LAAC;wCAAK,WAAU;kDAA+D;;;;;;;;;;;;0CAE/F,6LAAC;gCAAO,WAAU;0CAA0H;;;;;;;;;;;;kCAM9I,6LAAC;wBAAG,WAAU;kCAAoB;;;;;;kCAGlC,6LAAC;wBACC,MAAM;wBACN,QAAO;wBACP,KAAI;wBACJ,WAAU;kCACX;;;;;;kCAKD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAK3B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAqB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC5E,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjF;GAzFM;KAAA;uCA2FS", "debugId": null}}]}