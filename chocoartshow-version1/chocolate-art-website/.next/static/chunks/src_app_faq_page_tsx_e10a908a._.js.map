{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/chocolate-art/chocoartshow-version1/chocolate-art-website/src/app/faq/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function FAQPage() {\n  const [openItems, setOpenItems] = useState<number[]>([]);\n\n  const toggleItem = (index: number) => {\n    setOpenItems(prev => \n      prev.includes(index) \n        ? prev.filter(i => i !== index)\n        : [...prev, index]\n    );\n  };\n\n  const faqs = [\n    {\n      question: \"What is the Chocolate & Art Show?\",\n      answer: \"The Chocolate & Art Show is an immersive experience combining live art creation, artisan chocolate tastings, live music, and interactive experiences. Artists create works in real-time while guests enjoy premium chocolate and entertainment.\"\n    },\n    {\n      question: \"What's included with my ticket?\",\n      answer: \"Your ticket includes access to all live art demonstrations, chocolate tastings, live music performances, interactive art experiences, and body painting demonstrations. A cash bar is available for additional beverages.\"\n    },\n    {\n      question: \"Is this event 21+ only?\",\n      answer: \"Yes, this is a 21+ event. Valid government-issued photo ID is required for entry. No exceptions.\"\n    },\n    {\n      question: \"What time does the event start and end?\",\n      answer: \"Doors open at 7:00 PM both nights. The event runs until late, with last entry at 12:30 AM. We recommend arriving early to experience everything the show has to offer.\"\n    },\n    {\n      question: \"Where is the event located?\",\n      answer: \"The event takes place at Lofty Spaces in Dallas. The exact address and directions will be provided with your ticket confirmation.\"\n    },\n    {\n      question: \"Can I buy tickets at the door?\",\n      answer: \"We strongly recommend purchasing tickets in advance as this is a limited capacity event. Door sales are not guaranteed and will only be available if the event is not sold out.\"\n    },\n    {\n      question: \"What should I wear?\",\n      answer: \"The dress code is creative casual. Feel free to express yourself! Comfortable shoes are recommended as you'll be standing and walking around to view different art stations.\"\n    },\n    {\n      question: \"Can I take photos?\",\n      answer: \"Yes! Photography is encouraged. We love seeing guests share their experience on social media. Please be respectful of artists while they're working and ask permission before photographing people.\"\n    },\n    {\n      question: \"Is food available?\",\n      answer: \"Artisan chocolate tastings are included with your ticket. Light snacks may be available for purchase. Outside food and beverages are not permitted.\"\n    },\n    {\n      question: \"What if I have dietary restrictions?\",\n      answer: \"Please contact <NAME_EMAIL> before the event if you have severe allergies or dietary restrictions. We'll do our best to accommodate your needs.\"\n    },\n    {\n      question: \"Can I get a refund?\",\n      answer: \"All sales are final. However, if you cannot attend, you may transfer your ticket to someone else. Please contact us for assistance with ticket transfers.\"\n    },\n    {\n      question: \"How can I apply to be an artist or vendor?\",\n      answer: \"We're always looking for talented artists, vendors, and musicians! Visit our Artists page for application information or email us <NAME_EMAIL>.\"\n    }\n  ];\n\n  return (\n    <div className=\"pt-16\">\n      {/* Header */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 text-center\">\n        <div className=\"max-w-4xl mx-auto\">\n          <h1 className=\"text-5xl md:text-7xl font-black text-white mb-6\">\n            FAQ\n          </h1>\n          <p className=\"text-xl text-gray-300 mb-8\">\n            Everything you need to know about the Chocolate & Art Show\n          </p>\n        </div>\n      </section>\n\n      {/* FAQ Items */}\n      <section className=\"px-4 sm:px-6 lg:px-8 pb-20\">\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"space-y-4\">\n            {faqs.map((faq, index) => (\n              <div \n                key={index}\n                className=\"bg-white/5 border border-white/10 rounded-lg overflow-hidden\"\n              >\n                <button\n                  className=\"w-full px-6 py-4 text-left flex justify-between items-center hover:bg-white/5 transition-colors\"\n                  onClick={() => toggleItem(index)}\n                  aria-expanded={openItems.includes(index)}\n                >\n                  <span className=\"text-white font-semibold text-lg pr-4\">\n                    {faq.question}\n                  </span>\n                  <svg \n                    className={`w-5 h-5 text-gray-400 transform transition-transform ${\n                      openItems.includes(index) ? 'rotate-180' : ''\n                    }`}\n                    fill=\"none\" \n                    stroke=\"currentColor\" \n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n                \n                {openItems.includes(index) && (\n                  <div className=\"px-6 pb-4\">\n                    <p className=\"text-gray-300 leading-relaxed\">\n                      {faq.answer}\n                    </p>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Contact CTA */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-black/30 text-center\">\n        <div className=\"max-w-4xl mx-auto\">\n          <h2 className=\"text-4xl font-bold text-white mb-6\">\n            Still Have Questions?\n          </h2>\n          <p className=\"text-xl text-gray-300 mb-8\">\n            We're here to help! Reach out to us directly.\n          </p>\n          <a\n            href=\"mailto:<EMAIL>\"\n            className=\"inline-block bg-white/10 border border-white/30 text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-white/20 transition-all\"\n          >\n            Contact Us\n          </a>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAW,EAAE;IAEvD,MAAM,aAAa,CAAC;QAClB,aAAa,CAAA,OACX,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,SACvB;mBAAI;gBAAM;aAAM;IAExB;IAEA,MAAM,OAAO;QACX;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;0BAO9C,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCACC,WAAU;wCACV,SAAS,IAAM,WAAW;wCAC1B,iBAAe,UAAU,QAAQ,CAAC;;0DAElC,6LAAC;gDAAK,WAAU;0DACb,IAAI,QAAQ;;;;;;0DAEf,6LAAC;gDACC,WAAW,AAAC,wDAEX,OADC,UAAU,QAAQ,CAAC,SAAS,eAAe;gDAE7C,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;oCAIxE,UAAU,QAAQ,CAAC,wBAClB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDACV,IAAI,MAAM;;;;;;;;;;;;+BA1BZ;;;;;;;;;;;;;;;;;;;;0BAqCf,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAzIwB;KAAA", "debugId": null}}]}