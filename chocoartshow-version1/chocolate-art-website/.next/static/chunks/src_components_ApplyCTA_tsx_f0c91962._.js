(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/src/components/ApplyCTA.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
'use client';
;
const ApplyCTA = ()=>{
    const createGmailLink = (to, subject, body)=>{
        const encodedSubject = encodeURIComponent(subject);
        const encodedBody = encodeURIComponent(body);
        return "https://mail.google.com/mail/?view=cm&fs=1&to=".concat(encodeURIComponent(to), "&su=").concat(encodedSubject, "&body=").concat(encodedBody);
    };
    const applications = [
        {
            type: 'Artists',
            email: '<EMAIL>',
            subject: 'Artist Submission — Dallas Sept 18–19',
            body: 'Name:\nInstagram:\nPortfolio:\nMedium/Style:\nNight preference (Thu/Fri/Either):\nNotes:\n'
        },
        {
            type: 'Vendors',
            email: '<EMAIL>',
            subject: 'Vendor Application — Dallas Sept 18–19',
            body: 'Business name:\nProduct category:\nWebsite/Instagram:\nPower needs:\nNight preference:\nNotes:\n'
        },
        {
            type: 'Musicians/DJs',
            email: '<EMAIL>',
            subject: 'Musician/DJ Application — Dallas Sept 18–19',
            body: 'Act name:\nGenre:\nLinks (YouTube/SoundCloud/Instagram):\nTech rider (basic):\nAvailability (Thu/Fri):\nNotes:\n'
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
        className: "apply-cta",
        "aria-label": "Apply to Chocolate & Art Show — Dallas",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "apply-inner",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                    children: "Artists • Vendors • Musicians — Apply"
                }, void 0, false, {
                    fileName: "[project]/src/components/ApplyCTA.tsx",
                    lineNumber: 34,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    children: "Spots are limited. Dallas is almost sold out — email us to be considered."
                }, void 0, false, {
                    fileName: "[project]/src/components/ApplyCTA.tsx",
                    lineNumber: 35,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "btns",
                    children: applications.map((app, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                            className: "btn",
                            href: createGmailLink(app.email, app.subject, app.body),
                            target: "_blank",
                            rel: "noopener noreferrer",
                            children: [
                                app.type,
                                " apply"
                            ]
                        }, index, true, {
                            fileName: "[project]/src/components/ApplyCTA.tsx",
                            lineNumber: 38,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)))
                }, void 0, false, {
                    fileName: "[project]/src/components/ApplyCTA.tsx",
                    lineNumber: 36,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "microcopy",
                    children: "Space is extremely limited and nearly sold out. Email now to be considered—complete submissions receive priority."
                }, void 0, false, {
                    fileName: "[project]/src/components/ApplyCTA.tsx",
                    lineNumber: 49,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ApplyCTA.tsx",
            lineNumber: 33,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/ApplyCTA.tsx",
        lineNumber: 32,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_c = ApplyCTA;
const __TURBOPACK__default__export__ = ApplyCTA;
var _c;
__turbopack_context__.k.register(_c, "ApplyCTA");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
]);

//# sourceMappingURL=src_components_ApplyCTA_tsx_f0c91962._.js.map