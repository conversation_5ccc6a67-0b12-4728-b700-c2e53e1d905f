(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/src/components/NightSelector.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
const NightSelector = ()=>{
    _s();
    const [selectedNight, setSelectedNight] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const nights = [
        {
            id: 'thu',
            date: 'Thu • Sept 18',
            status: 'low',
            eventbriteUrl: 'https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=night_thu'
        },
        {
            id: 'fri',
            date: 'Fri • Sept 19',
            status: 'available',
            eventbriteUrl: 'https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=night_fri'
        }
    ];
    const handleNightClick = (night)=>{
        setSelectedNight(night.id);
        // Announce to screen readers
        const announcement = night.status === 'low' ? "".concat(night.date, " is almost sold out.") : "".concat(night.date, " selected.");
        // Create a temporary element for screen reader announcement
        const announcer = document.createElement('div');
        announcer.setAttribute('aria-live', 'polite');
        announcer.setAttribute('aria-atomic', 'true');
        announcer.className = 'sr-only';
        announcer.textContent = announcement;
        document.body.appendChild(announcer);
        setTimeout(()=>{
            document.body.removeChild(announcer);
        }, 1000);
        // Open Eventbrite
        window.open(night.eventbriteUrl, '_blank', 'noopener');
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
        className: "night-select",
        "aria-label": "Choose your night",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col sm:flex-row gap-4 justify-center",
                children: nights.map((night)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        className: "night ".concat(selectedNight === night.id ? 'selected' : ''),
                        onClick: ()=>handleNightClick(night),
                        "aria-describedby": night.status === 'low' ? "".concat(night.id, "-status") : undefined,
                        children: [
                            night.date,
                            night.status === 'low' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "tag",
                                "aria-hidden": "true",
                                children: "Almost Gone"
                            }, void 0, false, {
                                fileName: "[project]/src/components/NightSelector.tsx",
                                lineNumber: 58,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, night.id, true, {
                        fileName: "[project]/src/components/NightSelector.tsx",
                        lineNumber: 50,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)))
            }, void 0, false, {
                fileName: "[project]/src/components/NightSelector.tsx",
                lineNumber: 48,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "sr-only",
                "aria-live": "polite",
                "aria-atomic": "true"
            }, void 0, false, {
                fileName: "[project]/src/components/NightSelector.tsx",
                lineNumber: 67,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            nights.map((night)=>night.status === 'low' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    id: "".concat(night.id, "-status"),
                    className: "sr-only",
                    children: "Almost sold out"
                }, "".concat(night.id, "-status"), false, {
                    fileName: "[project]/src/components/NightSelector.tsx",
                    lineNumber: 72,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0)))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/NightSelector.tsx",
        lineNumber: 47,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(NightSelector, "Tzbqwx9TicMtd+3MDuRruAMYi8o=");
_c = NightSelector;
const __TURBOPACK__default__export__ = NightSelector;
var _c;
__turbopack_context__.k.register(_c, "NightSelector");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
]);

//# sourceMappingURL=src_components_NightSelector_tsx_960f09cb._.js.map