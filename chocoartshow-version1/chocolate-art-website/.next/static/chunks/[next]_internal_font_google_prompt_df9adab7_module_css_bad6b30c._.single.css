/* [next]/internal/font/google/prompt_df9adab7.module.css [app-client] (css) */
@font-face {
  font-family: Prompt;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/89970c532165c566-s.999ce90f.woff2") format("woff2");
  unicode-range: U+2D7, U+303, U+331, U+E01-E5B, U+200C-200D, U+25CC;
}

@font-face {
  font-family: Prompt;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/54a20415383d1725-s.76704a21.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Prompt;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/bd2ede351b1cefaf-s.31334d01.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Prompt;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/6958aadccb02a37a-s.p.d94e1675.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Prompt;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/4341de68155aee74-s.04718d15.woff2") format("woff2");
  unicode-range: U+2D7, U+303, U+331, U+E01-E5B, U+200C-200D, U+25CC;
}

@font-face {
  font-family: Prompt;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/1360732955527ddf-s.e7ccd9ae.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Prompt;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/48c50b94fd699803-s.2910bb7b.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Prompt;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/ab24015c41b3954b-s.p.041a85fb.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Prompt;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("../media/a11fc3c3e4a74895-s.b4e11a71.woff2") format("woff2");
  unicode-range: U+2D7, U+303, U+331, U+E01-E5B, U+200C-200D, U+25CC;
}

@font-face {
  font-family: Prompt;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("../media/672fae3e42f3dcfa-s.129593af.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Prompt;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("../media/ced40c7946d7ed3a-s.f6d7b51d.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Prompt;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("../media/6bdd97a4aff75f68-s.p.76ccd01d.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Prompt Fallback;
  src: local(Arial);
  ascent-override: 97.38%;
  descent-override: 37.7%;
  line-gap-override: 0.0%;
  size-adjust: 111.93%;
}

.prompt_df9adab7-module__Up_5VW__className {
  font-family: Prompt, Prompt Fallback;
  font-style: normal;
}

.prompt_df9adab7-module__Up_5VW__variable {
  --font-prompt: "Prompt", "Prompt Fallback";
}

/*# sourceMappingURL=%5Bnext%5D_internal_font_google_prompt_df9adab7_module_css_bad6b30c._.single.css.map*/