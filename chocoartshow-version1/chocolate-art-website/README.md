# Chocolate & Art Show — Dallas Website

A fully responsive, accessible website for the Chocolate & Art Show in Dallas, built with Next.js 14, TypeScript, and Tailwind CSS.

## 🎨 Features

### Visual Design
- **Textured black background** with noise overlay for visual depth
- **Waterfall/perspective headlines** with scroll-linked animations
- **RGB big-type wall** interstitial sections
- **Gradient text shadow CTAs** for primary actions
- **Organic blob masks** for artist images using SVG clip-paths
- **Animated backgrounds** with reduced motion support

### Pages & Functionality
- **Home** - Hero with video background, RGB wall, gallery teaser, social proof
- **Artists** - 5-card grid with blob masks, individual artist detail pages, apply CTAs
- **Tickets** - Night selector, pricing grid, CTA boxes with animated borders
- **Gallery** - Image grid with category filtering
- **Schedule** - Detailed timeline for both event nights
- **FAQ** - Accordion-style questions and answers
- **Contact** - Contact information and venue details
- **Kusama Playground** - Interactive dot animation inspired by Yayoi <PERSON>sama

### Accessibility & Performance
- **WCAG 2.1 AA compliant** with proper focus states and ARIA labels
- **Reduced motion support** for all animations
- **Mobile-first responsive design**
- **SEO optimized** with structured data (JSON-LD)
- **Performance optimized** with Next.js Image component and lazy loading

### Integration Ready
- **Eventbrite tracking** with UTM parameters
- **Gmail compose links** for artist applications
- **Headless WordPress ready** structure for content management
- **Social media integration** placeholders

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd chocolate-art-website
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
src/
├── app/                    # Next.js 14 App Router
│   ├── artists/           # Artists page and individual artist routes
│   ├── contact/           # Contact page
│   ├── faq/              # FAQ page
│   ├── gallery/          # Gallery page
│   ├── play/kusama/      # Kusama playground
│   ├── schedule/         # Schedule page
│   ├── tickets/          # Tickets page
│   ├── globals.css       # Global styles and visual effects
│   ├── layout.tsx        # Root layout with navigation and metadata
│   └── page.tsx          # Home page
└── components/            # Reusable React components
    ├── ApplyCTA.tsx      # Artist/vendor application CTAs
    ├── ArtistGrid.tsx    # Artist grid with blob masks
    ├── CTABox.tsx        # Animated CTA boxes
    ├── GalleryTeaser.tsx # Gallery preview section
    ├── HeroSection.tsx   # Home page hero with video background
    ├── Navigation.tsx    # Main navigation component
    ├── NightSelector.tsx # Event night selection
    ├── PricingGrid.tsx   # Ticket pricing options
    ├── RGBWall.tsx       # RGB interstitial component
    ├── SocialProof.tsx   # Testimonials and social proof
    ├── UrgencyStrip.tsx  # Urgency messaging
    └── WhatIsSection.tsx # Event description section
```

## 🎯 Key Visual Effects

### Waterfall Headlines
3D perspective text with scroll-linked animation that respects reduced motion preferences.

### RGB Wall
Large typography with RGB offset effects and animated gradient CTAs.

### Blob Masks
Organic SVG clip-paths for artist images with fallbacks for unsupported browsers.

### Textured Background
Subtle noise overlay using SVG filters for visual depth without performance impact.

## 🔗 External Integrations

### Eventbrite
All ticket links include proper UTM tracking:
```
https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign={slot}
```

### Gmail Applications
Artist/vendor applications open Gmail compose with pre-filled subjects and bodies.

### WordPress Ready
Component structure designed for easy integration with headless WordPress:
- Props-based components for dynamic content
- TypeScript interfaces for data structures
- Environment variable configuration

## 🛠 Customization

### Content
- Artist data: `src/app/artists/[slug]/page.tsx`
- Event details: `src/app/layout.tsx` (JSON-LD)
- Contact information: `src/app/contact/page.tsx`

### Eventbrite URLs
Update the base URL in all components:
```typescript
const eventbriteUrl = "https://www.eventbrite.com/e/your-event-id";
```

## 📱 Browser Support

- Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- Graceful degradation for CSS clip-path, scroll-timeline, and reduced motion

## 🚀 Deployment

### Vercel (Recommended)
```bash
npm run build
vercel --prod
```

## 📞 Support

For questions or support, contact: <EMAIL>
