import Link from 'next/link';
import Image from 'next/image';

const ArtistGrid = () => {
  const artists = [
    {
      id: 'danica',
      name: '<PERSON><PERSON>',
      image: '/assets/artists/danica/thumb.jpg',
      alt: '<PERSON><PERSON> — portrait',
      blobClass: 'blob-1'
    },
    {
      id: 'david-v',
      name: '<PERSON>',
      image: '/assets/artists/david-v/thumb.jpg',
      alt: '<PERSON> portrait with hat',
      blobClass: 'blob-2'
    },
    {
      id: 'omi',
      name: '<PERSON><PERSON>',
      image: '/assets/artists/omi/thumb.jpg',
      alt: '<PERSON><PERSON> — profile with sunglasses',
      blobClass: 'blob-3'
    },
    {
      id: 'sofia',
      name: '<PERSON><PERSON><PERSON>',
      image: '/assets/artists/sofia/thumb.jpg',
      alt: '<PERSON><PERSON><PERSON> — portrait with hat',
      blobClass: 'blob-4'
    },
    {
      id: 'leon',
      name: '<PERSON>',
      image: '/assets/artists/leon/thumb.jpg',
      alt: '<PERSON> — studio portrait',
      blobClass: 'blob-5'
    }
  ];

  return (
    <section className="artist-preview" aria-label="Featured artists — Dallas">
      {/* SVG clip-path definitions */}
      <svg className="visually-hidden" aria-hidden="true" width="0" height="0" focusable="false">
        <defs>
          <clipPath id="blob-1" clipPathUnits="objectBoundingBox">
            <path d="M0.02,0.42 C0.05,0.22 0.20,0.06 0.40,0.06 L0.88,0.06 C0.96,0.06 0.99,0.16 0.98,0.30 C0.96,0.46 0.86,0.66 0.66,0.70 C0.49,0.74 0.40,0.67 0.27,0.72 C0.15,0.76 0.05,0.68 0.03,0.55 Z" />
          </clipPath>
          <clipPath id="blob-2" clipPathUnits="objectBoundingBox">
            <path d="M0.03,0.38 C0.09,0.16 0.23,0.06 0.45,0.06 L0.86,0.06 C0.96,0.06 1.00,0.18 0.98,0.34 C0.96,0.50 0.87,0.69 0.68,0.73 C0.50,0.77 0.38,0.68 0.24,0.74 C0.12,0.78 0.01,0.70 0.02,0.55 Z" />
          </clipPath>
          <clipPath id="blob-3" clipPathUnits="objectBoundingBox">
            <path d="M0.04,0.40 C0.07,0.20 0.22,0.07 0.42,0.07 L0.85,0.07 C0.96,0.07 1.00,0.20 0.98,0.35 C0.95,0.52 0.85,0.70 0.64,0.73 C0.44,0.77 0.34,0.67 0.20,0.73 C0.09,0.77 0.01,0.68 0.02,0.53 Z" />
          </clipPath>
          <clipPath id="blob-4" clipPathUnits="objectBoundingBox">
            <path d="M0.02,0.36 C0.06,0.17 0.21,0.05 0.39,0.05 L0.86,0.05 C0.96,0.05 1.00,0.17 0.98,0.32 C0.95,0.49 0.85,0.69 0.65,0.73 C0.47,0.76 0.36,0.68 0.22,0.74 C0.10,0.78 0.02,0.70 0.02,0.56 Z" />
          </clipPath>
          <clipPath id="blob-5" clipPathUnits="objectBoundingBox">
            <path d="M0.03,0.39 C0.08,0.18 0.23,0.06 0.41,0.06 L0.87,0.06 C0.97,0.06 1.00,0.17 0.98,0.33 C0.96,0.49 0.86,0.68 0.67,0.72 C0.49,0.76 0.39,0.68 0.26,0.73 C0.14,0.77 0.04,0.70 0.03,0.56 Z" />
          </clipPath>
        </defs>
      </svg>

      <ul className="artist-grid" role="list">
        {artists.map((artist) => (
          <li key={artist.id} className="artist-card">
            <Link
              className="artist-link"
              href={`/artists/${artist.id}`}
              aria-label={`View ${artist.name} — bio and works`}
            >
              <figure className={`media ${artist.blobClass}`}>
                <Image
                  src={artist.image}
                  alt={artist.alt}
                  width={640}
                  height={420}
                  loading="lazy"
                  className="w-full h-full object-cover"
                />
              </figure>
              <span className="name">{artist.name}</span>
            </Link>
          </li>
        ))}
      </ul>
    </section>
  );
};

export default ArtistGrid;
