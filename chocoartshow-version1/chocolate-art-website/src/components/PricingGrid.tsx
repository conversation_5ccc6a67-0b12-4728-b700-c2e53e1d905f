const PricingGrid = () => {
  const ticketTypes = [
    {
      name: 'General Admission',
      price: '$35',
      originalPrice: null,
      description: 'Access to all art experiences, chocolate tastings, and live music',
      features: [
        'Full event access',
        'Chocolate tastings',
        'Live art viewing',
        'Music performances',
        'Interactive experiences'
      ],
      popular: false,
      eventbriteUrl: 'https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=pricing_general'
    },
    {
      name: 'VIP Experience',
      price: '$65',
      originalPrice: '$75',
      description: 'Premium access with exclusive perks and priority entry',
      features: [
        'Everything in General',
        'Priority entry',
        'VIP chocolate selection',
        'Meet the artists',
        'Complimentary drink',
        'VIP seating area'
      ],
      popular: true,
      eventbriteUrl: 'https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=pricing_vip'
    },
    {
      name: 'Artist Pass',
      price: '$25',
      originalPrice: null,
      description: 'Special pricing for fellow artists and creatives',
      features: [
        'Full event access',
        'Chocolate tastings',
        'Artist networking',
        'Portfolio review opportunity',
        'Valid art student ID required'
      ],
      popular: false,
      eventbriteUrl: 'https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=pricing_artist'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      {ticketTypes.map((ticket, index) => (
        <div 
          key={index}
          className={`relative bg-white/5 border rounded-2xl p-6 ${
            ticket.popular 
              ? 'border-purple-400 ring-2 ring-purple-400/20' 
              : 'border-white/10'
          }`}
        >
          {ticket.popular && (
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <span className="bg-purple-500 text-white px-4 py-1 rounded-full text-sm font-bold">
                Most Popular
              </span>
            </div>
          )}
          
          <div className="text-center mb-6">
            <h3 className="text-2xl font-bold text-white mb-2">{ticket.name}</h3>
            <div className="flex items-center justify-center gap-2 mb-2">
              <span className="text-4xl font-black text-white">{ticket.price}</span>
              {ticket.originalPrice && (
                <span className="text-lg text-gray-400 line-through">{ticket.originalPrice}</span>
              )}
            </div>
            <p className="text-gray-300 text-sm">{ticket.description}</p>
          </div>

          <ul className="space-y-3 mb-8">
            {ticket.features.map((feature, featureIndex) => (
              <li key={featureIndex} className="flex items-center text-gray-200">
                <svg className="w-5 h-5 text-green-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                {feature}
              </li>
            ))}
          </ul>

          <a
            href={ticket.eventbriteUrl}
            target="_blank"
            rel="noopener noreferrer"
            className={`block w-full text-center py-3 px-6 rounded-lg font-bold transition-all ${
              ticket.popular
                ? 'bg-purple-600 text-white hover:bg-purple-700'
                : 'bg-white/10 text-white border border-white/20 hover:bg-white/20'
            }`}
          >
            Select {ticket.name}
          </a>
        </div>
      ))}
    </div>
  );
};

export default PricingGrid;
