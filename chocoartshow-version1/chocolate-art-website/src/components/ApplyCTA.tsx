'use client';

const ApplyCTA = () => {
  const createGmailLink = (to: string, subject: string, body: string) => {
    const encodedSubject = encodeURIComponent(subject);
    const encodedBody = encodeURIComponent(body);
    return `https://mail.google.com/mail/?view=cm&fs=1&to=${encodeURIComponent(to)}&su=${encodedSubject}&body=${encodedBody}`;
  };

  const applications = [
    {
      type: 'Artists',
      email: '<EMAIL>',
      subject: 'Artist Submission — Dallas Sept 18–19',
      body: 'Name:\nInstagram:\nPortfolio:\nMedium/Style:\nNight preference (Thu/Fri/Either):\nNotes:\n'
    },
    {
      type: 'Vendors',
      email: '<EMAIL>',
      subject: 'Vendor Application — Dallas Sept 18–19',
      body: 'Business name:\nProduct category:\nWebsite/Instagram:\nPower needs:\nNight preference:\nNotes:\n'
    },
    {
      type: 'Musicians/DJs',
      email: '<EMAIL>',
      subject: 'Musician/DJ Application — Dallas Sept 18–19',
      body: 'Act name:\nGenre:\nLinks (YouTube/SoundCloud/Instagram):\nTech rider (basic):\nAvailability (Thu/Fri):\nNotes:\n'
    }
  ];

  return (
    <section className="apply-cta" aria-label="Apply to Chocolate & Art Show — Dallas">
      <div className="apply-inner">
        <h2>Artists • Vendors • Musicians — Apply</h2>
        <p>Spots are limited. Dallas is almost sold out — email us to be considered.</p>
        <div className="btns">
          {applications.map((app, index) => (
            <a
              key={index}
              className="btn"
              href={createGmailLink(app.email, app.subject, app.body)}
              target="_blank"
              rel="noopener noreferrer"
            >
              {app.type} apply
            </a>
          ))}
        </div>
        <p className="microcopy">
          Space is extremely limited and nearly sold out. Email now to be considered—complete submissions receive priority.
        </p>
      </div>
    </section>
  );
};

export default ApplyCTA;
