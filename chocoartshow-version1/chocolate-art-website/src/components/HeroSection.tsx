'use client';

import { useEffect, useState } from 'react';

const HeroSection = () => {
  const [isReducedMotion, setIsReducedMotion] = useState(false);

  useEffect(() => {
    // Check for reduced motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setIsReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setIsReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  const eventbriteUrl = "https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=hero";

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Video */}
      <div className="absolute inset-0 z-0">
        <div className="video-bg">
          <iframe 
            src="https://www.youtube.com/embed/dQw4w9WgXcQ?start=30&autoplay=1&mute=1&loop=1&playlist=dQw4w9WgXcQ&controls=0&rel=0&playsinline=1" 
            allow="autoplay" 
            frameBorder="0"
            className="absolute top-1/2 left-1/2 w-[177.777vh] min-w-full h-screen transform -translate-x-1/2 -translate-y-1/2"
          />
        </div>
        <div className="absolute inset-0 bg-black/55 z-10" />
      </div>

      {/* Hero Content */}
      <div className="relative z-20 text-center px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        {/* Waterfall Headline */}
        <div 
          className={`waterfall mb-8 ${isReducedMotion ? 'transform-none' : ''}`} 
          aria-label="Chocolate and Art Show Dallas"
        >
          <span className="line">Chocolate &</span>
          <span className="line">Art Show</span>
          <span className="line">Dallas</span>
        </div>

        {/* Subtitle */}
        <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto">
          Immersive art, live music, body painting, and artisan chocolate. Two nights only in Dallas.
        </p>

        {/* Date Selector */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
          <button className="night-btn bg-white/10 border border-white/30 text-white px-6 py-3 rounded-lg font-bold hover:bg-white/20 transition-all">
            Thu • Sept 18 <span className="ml-2 text-xs bg-yellow-400 text-black px-2 py-1 rounded-full">Almost Gone</span>
          </button>
          <button className="night-btn bg-white/10 border border-white/30 text-white px-6 py-3 rounded-lg font-bold hover:bg-white/20 transition-all">
            Fri • Sept 19
          </button>
        </div>

        {/* CTA Gradient Headline */}
        <h2 className="cta-gradient mb-6">Buy Dallas Tickets</h2>

        {/* Main CTA Button */}
        <a
          href={eventbriteUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-block bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-lg font-bold text-lg hover:from-blue-600 hover:to-purple-700 transition-all transform hover:scale-105"
        >
          Get Tickets Now
        </a>

        {/* Event Details */}
        <div className="mt-8 text-gray-300">
          <p className="text-sm">21+ • Lofty Spaces • Dallas</p>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="animate-bounce">
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
