const SocialProof = () => {
  const testimonials = [
    {
      id: 1,
      text: "An incredible night of art, music, and chocolate. Unlike anything I've experienced in Dallas!",
      author: "<PERSON>",
      event: "Previous Show Attendee"
    },
    {
      id: 2,
      text: "The live art creation while enjoying artisan chocolate was absolutely magical.",
      author: "<PERSON>",
      event: "Art Enthusiast"
    },
    {
      id: 3,
      text: "Perfect date night! The atmosphere was electric and the artists were so talented.",
      author: "<PERSON> & Tom",
      event: "<PERSON><PERSON><PERSON>'s Night Out"
    }
  ];

  const stats = [
    { number: "15", label: "Years Running" },
    { number: "500+", label: "Artists Featured" },
    { number: "10K+", label: "Happy Guests" },
    { number: "2", label: "Nights Only" }
  ];

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/30">
      <div className="max-w-7xl mx-auto">
        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-4xl md:text-5xl font-black text-white mb-2">
                {stat.number}
              </div>
              <div className="text-gray-400 font-semibold">
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* Testimonials */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-black text-white mb-12">
            What People Are Saying
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial) => (
              <div key={testimonial.id} className="bg-white/5 border border-white/10 rounded-lg p-6 backdrop-blur-sm">
                <p className="text-gray-200 italic mb-4 text-lg leading-relaxed">
                  {testimonial.text}
                </p>
                <div className="text-sm">
                  <div className="text-white font-semibold">{testimonial.author}</div>
                  <div className="text-gray-400">{testimonial.event}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* iMessage Style FOMO Chat */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-gray-900/50 rounded-2xl p-6 border border-white/10">
            <div className="space-y-4">
              <div className="flex justify-start">
                <div className="bg-gray-300 text-black rounded-2xl rounded-bl-md px-4 py-2 max-w-xs">
                  Thu — The Dallas Stones are playing.
                </div>
              </div>

              <div className="flex justify-end">
                <div className="bg-blue-500 text-white rounded-2xl rounded-br-md px-4 py-2 max-w-xs">
                  Friday is DJ Omu.
                </div>
              </div>

              <div className="flex justify-start">
                <div className="bg-gray-300 text-black rounded-2xl rounded-bl-md px-4 py-2 max-w-xs">
                  Then we better go both nights.
                </div>
              </div>

              <div className="flex justify-end">
                <div className="bg-blue-500 text-white rounded-2xl rounded-br-md px-4 py-2 max-w-xs">
                  Hope we can get in — it&apos;s almost sold out.
                  <a
                    href="https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=imessage"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline font-bold ml-1"
                  >
                    Grab tickets
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SocialProof;
