import Link from 'next/link';
import Image from 'next/image';

const GalleryTeaser = () => {
  // Placeholder images - these would be replaced with actual gallery images
  const galleryImages = [
    {
      id: 1,
      src: 'public/assets/gallery-teaser/1.jpg',
      alt: 'Live painting at Chocolate & Art Show',
      title: 'Live Art Creation'
    },
    {
      id: 2,
      src: '/api/placeholder/400/300',
      alt: 'Artist working on canvas',
      title: 'Interactive Experience'
    },
    {
      id: 3,
      src: '/api/placeholder/400/300',
      alt: 'Chocolate tasting station',
      title: 'Artisan Chocolate'
    },
    {
      id: 4,
      src: '/api/placeholder/400/300',
      alt: 'Live music performance',
      title: 'Live Music'
    },
    {
      id: 5,
      src: '/api/placeholder/400/300',
      alt: 'Body painting art',
      title: 'Body Art'
    },
    {
      id: 6,
      src: '/api/placeholder/400/300',
      alt: 'Event atmosphere',
      title: 'Night Atmosphere'
    }
  ];

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-6xl font-black text-white mb-4">
            Experience the Magic
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Get a glimpse of what awaits you at Dallas's most immersive art experience
          </p>
        </div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {galleryImages.map((image) => (
            <div
              key={image.id}
              className="group relative overflow-hidden rounded-lg bg-gray-800 aspect-[4/3] hover:transform hover:scale-105 transition-all duration-300"
            >
              <Image
                src={image.src}
                alt={image.alt}
                fill
                className="object-cover group-hover:opacity-80 transition-opacity"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
              <div className="absolute bottom-4 left-4 right-4">
                <h3 className="text-white font-bold text-lg">{image.title}</h3>
              </div>
            </div>
          ))}
        </div>

        {/* CTA to Gallery */}
        <div className="text-center">
          <Link
            href="/gallery"
            className="inline-block bg-white/10 border border-white/30 text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-white/20 transition-all"
          >
            View Full Gallery
          </Link>
        </div>
      </div>
    </section>
  );
};

export default GalleryTeaser;
