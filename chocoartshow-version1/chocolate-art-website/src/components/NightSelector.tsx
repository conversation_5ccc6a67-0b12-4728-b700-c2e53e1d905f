'use client';

import { useState } from 'react';

const NightSelector = () => {
  const [selectedNight, setSelectedNight] = useState<string | null>(null);

  const nights = [
    {
      id: 'thu',
      date: 'Thu • Sept 18',
      status: 'low',
      eventbriteUrl: 'https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=night_thu'
    },
    {
      id: 'fri',
      date: 'Fri • Sept 19',
      status: 'available',
      eventbriteUrl: 'https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089?aff=site&utm_source=site&utm_medium=cta&utm_campaign=night_fri'
    }
  ];

  const handleNightClick = (night: typeof nights[0]) => {
    setSelectedNight(night.id);
    // Announce to screen readers
    const announcement = night.status === 'low' 
      ? `${night.date} is almost sold out.` 
      : `${night.date} selected.`;
    
    // Create a temporary element for screen reader announcement
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', 'polite');
    announcer.setAttribute('aria-atomic', 'true');
    announcer.className = 'sr-only';
    announcer.textContent = announcement;
    document.body.appendChild(announcer);
    
    setTimeout(() => {
      document.body.removeChild(announcer);
    }, 1000);

    // Open Eventbrite
    window.open(night.eventbriteUrl, '_blank', 'noopener');
  };

  return (
    <section className="night-select" aria-label="Choose your night">
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        {nights.map((night) => (
          <button
            key={night.id}
            className={`night ${selectedNight === night.id ? 'selected' : ''}`}
            onClick={() => handleNightClick(night)}
            aria-describedby={night.status === 'low' ? `${night.id}-status` : undefined}
          >
            {night.date}
            {night.status === 'low' && (
              <span className="tag" aria-hidden="true">
                Almost&nbsp;Gone
              </span>
            )}
          </button>
        ))}
      </div>
      
      {/* Screen reader announcements */}
      <div className="sr-only" aria-live="polite" aria-atomic="true"></div>
      
      {/* Hidden status descriptions for screen readers */}
      {nights.map((night) => (
        night.status === 'low' && (
          <div key={`${night.id}-status`} id={`${night.id}-status`} className="sr-only">
            Almost sold out
          </div>
        )
      ))}
    </section>
  );
};

export default NightSelector;
