const WhatIsSection = () => {
  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* EXPLORE Column */}
          <div className="space-y-4">
            <h2 className="text-4xl md:text-5xl font-black text-white leading-tight">
              EXPLORE
            </h2>
            <div className="space-y-2 text-lg leading-relaxed text-gray-300">
              <p>Where art meets indulgence</p>
              <p>Live creation in real time</p>
              <p>Interactive experiences</p>
              <p>Sensory immersion</p>
              <p>Community connection</p>
            </div>
          </div>

          {/* DISCOVER Column */}
          <div className="space-y-6">
            <div>
              <span className="text-sm uppercase tracking-wider text-gray-400 font-semibold">
                What to Expect
              </span>
              <h2 className="text-4xl md:text-5xl font-black text-white mt-2">
                DISCOVER
              </h2>
            </div>
            
            <div className="space-y-4">
              <div className="flex space-x-6 text-sm font-semibold">
                <button className="text-white border-b-2 border-blue-400 pb-1">Artists</button>
                <button className="text-gray-400 hover:text-white transition-colors">Music</button>
                <button className="text-gray-400 hover:text-white transition-colors">Chocolate</button>
              </div>
              
              <div className="text-gray-300">
                <p className="mb-4">
                  Watch local and emerging artists create live paintings, sculptures, and digital art 
                  while you enjoy artisan chocolate and craft cocktails.
                </p>
                <p>
                  Each night features different artists, musicians, and chocolate makers, 
                  creating a unique experience every time.
                </p>
              </div>
            </div>
          </div>

          {/* IMAGINE Column */}
          <div className="space-y-4">
            <h2 className="text-4xl md:text-5xl font-black text-white leading-tight">
              IMAGINE
            </h2>
            <div className="bg-gradient-to-br from-purple-900/30 to-blue-900/30 p-6 rounded-lg border border-white/10">
              <p className="text-lg text-gray-200 italic leading-relaxed">
                "A night where creativity flows as freely as the chocolate, 
                where every brushstroke tells a story, and every bite is an experience."
              </p>
              <div className="mt-4 text-sm text-gray-400">
                — What guests are saying
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhatIsSection;
