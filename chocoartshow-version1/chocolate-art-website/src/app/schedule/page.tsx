export const metadata = {
  title: 'Schedule — Chocolate & Art Show Dallas',
  description: 'View the schedule for both nights of the Chocolate & Art Show in Dallas.',
};

const SchedulePage = () => {
  const thursdaySchedule = [
    { time: '7:00 PM', event: 'Doors Open', description: 'Welcome reception with chocolate tastings' },
    { time: '7:30 PM', event: 'Live Art Begins', description: 'Artists start creating live works' },
    { time: '8:00 PM', event: 'The Dallas Stones', description: 'Live music performance' },
    { time: '9:00 PM', event: 'Body Painting Demo', description: 'Interactive body art demonstration' },
    { time: '10:00 PM', event: 'Artist Spotlight', description: 'Featured artist presentation' },
    { time: '11:00 PM', event: 'Chocolate Pairing', description: 'Guided chocolate and wine pairing' },
    { time: '12:00 AM', event: 'Late Night Art', description: 'Continued live art creation' },
    { time: '12:30 AM', event: 'Last Entry', description: 'Final opportunity to enter' },
    { time: '2:00 AM', event: 'Event Ends', description: 'Closing and artwork viewing' }
  ];

  const fridaySchedule = [
    { time: '7:00 PM', event: 'Doors Open', description: 'Welcome reception with chocolate tastings' },
    { time: '7:30 PM', event: 'Live Art Begins', description: 'Artists start creating live works' },
    { time: '8:00 PM', event: 'DJ Omu', description: 'Electronic music sets' },
    { time: '9:00 PM', event: 'Interactive Art Station', description: 'Guests create alongside artists' },
    { time: '10:00 PM', event: 'Chocolate Making Demo', description: 'Learn from master chocolatiers' },
    { time: '11:00 PM', event: 'Live Collaboration', description: 'Artists collaborate on large piece' },
    { time: '12:00 AM', event: 'DJ Omu Returns', description: 'Late night music continues' },
    { time: '12:30 AM', event: 'Last Entry', description: 'Final opportunity to enter' },
    { time: '2:00 AM', event: 'Event Ends', description: 'Closing and artwork viewing' }
  ];

  return (
    <div className="pt-16">
      {/* Header */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-7xl font-black text-white mb-6">
            Schedule
          </h1>
          <p className="text-xl text-gray-300 mb-8">
            Two unique nights of art, music, and chocolate
          </p>
        </div>
      </section>

      {/* Schedule Grid */}
      <section className="px-4 sm:px-6 lg:px-8 pb-20">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            
            {/* Thursday Schedule */}
            <div>
              <div className="text-center mb-8">
                <h2 className="text-4xl font-bold text-white mb-2">Thursday, Sept 18</h2>
                <p className="text-gray-300">Featuring The Dallas Stones</p>
                <span className="inline-block bg-yellow-500 text-black px-3 py-1 rounded-full text-sm font-bold mt-2">
                  Almost Sold Out
                </span>
              </div>
              
              <div className="space-y-4">
                {thursdaySchedule.map((item, index) => (
                  <div key={index} className="bg-white/5 border border-white/10 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-4 mb-2">
                          <span className="text-blue-400 font-bold text-lg min-w-[80px]">
                            {item.time}
                          </span>
                          <h3 className="text-white font-bold text-lg">
                            {item.event}
                          </h3>
                        </div>
                        <p className="text-gray-300 ml-[96px]">
                          {item.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Friday Schedule */}
            <div>
              <div className="text-center mb-8">
                <h2 className="text-4xl font-bold text-white mb-2">Friday, Sept 19</h2>
                <p className="text-gray-300">Featuring DJ Omu</p>
                <span className="inline-block bg-green-500 text-black px-3 py-1 rounded-full text-sm font-bold mt-2">
                  Tickets Available
                </span>
              </div>
              
              <div className="space-y-4">
                {fridaySchedule.map((item, index) => (
                  <div key={index} className="bg-white/5 border border-white/10 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-4 mb-2">
                          <span className="text-purple-400 font-bold text-lg min-w-[80px]">
                            {item.time}
                          </span>
                          <h3 className="text-white font-bold text-lg">
                            {item.event}
                          </h3>
                        </div>
                        <p className="text-gray-300 ml-[96px]">
                          {item.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

          </div>
        </div>
      </section>

      {/* Important Notes */}
      <section className="py-12 px-4 sm:px-6 lg:px-8 bg-black/30">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Important Notes</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white/5 border border-white/10 rounded-lg p-6">
              <h3 className="text-xl font-bold text-white mb-3">General Information</h3>
              <ul className="text-gray-300 space-y-2">
                <li>• Schedule subject to change</li>
                <li>• All times are approximate</li>
                <li>• Artists work throughout the evening</li>
                <li>• Multiple activities happen simultaneously</li>
              </ul>
            </div>
            <div className="bg-white/5 border border-white/10 rounded-lg p-6">
              <h3 className="text-xl font-bold text-white mb-3">What to Expect</h3>
              <ul className="text-gray-300 space-y-2">
                <li>• Continuous chocolate tastings</li>
                <li>• Interactive art opportunities</li>
                <li>• Meet and greet with artists</li>
                <li>• Photo opportunities throughout</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold text-white mb-6">
            Choose Your Night
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Each night offers a unique experience with different artists and performers
          </p>
          <a
            href="/tickets"
            className="inline-block bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-lg font-bold text-lg hover:from-blue-600 hover:to-purple-700 transition-all"
          >
            Get Your Tickets
          </a>
        </div>
      </section>
    </div>
  );
};

export default SchedulePage;
