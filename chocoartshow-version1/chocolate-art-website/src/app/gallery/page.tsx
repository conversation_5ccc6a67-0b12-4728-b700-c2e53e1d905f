import Image from 'next/image';

export const metadata = {
  title: 'Gallery — Chocolate & Art Show Dallas',
  description: 'View photos and artwork from previous Chocolate & Art Shows in Dallas.',
};

const GalleryPage = () => {
  // Gallery images from the event
  const galleryImages = [
    {
      id: 1,
      src: '/assets/gallery/live-art-1.jpg',
      alt: 'Live painting at Chocolate & Art Show',
      title: 'Live Art Creation',
      category: 'Live Art'
    },
    {
      id: 2,
      src: '/assets/gallery/behind-scenes-1.jpg',
      alt: 'Artist working on canvas',
      title: 'Interactive Experience',
      category: 'Behind the Scenes'
    },
    {
      id: 3,
      src: '/assets/gallery/chocolate-1.jpg',
      alt: 'Chocolate tasting station',
      title: 'Artisan Chocolate',
      category: 'Food & Drink'
    },
    {
      id: 4,
      src: '/assets/gallery/music-1.jpg',
      alt: 'Live music performance',
      title: 'Live Music',
      category: 'Music'
    },
    {
      id: 5,
      src: '/assets/gallery/live-art-2.jpg',
      alt: 'Body painting art',
      title: 'Body Art',
      category: 'Live Art'
    },
    {
      id: 6,
      src: '/assets/gallery/atmosphere-1.jpg',
      alt: 'Event atmosphere',
      title: 'Night Atmosphere',
      category: 'Atmosphere'
    },
    {
      id: 7,
      src: '/assets/gallery/atmosphere-2.jpg',
      alt: 'Crowd enjoying the show',
      title: 'Engaged Audience',
      category: 'Atmosphere'
    },
    {
      id: 8,
      src: '/assets/gallery/artwork-1.jpg',
      alt: 'Finished artwork display',
      title: 'Completed Pieces',
      category: 'Artwork'
    },
    {
      id: 9,
      src: '/assets/gallery/behind-scenes-2.jpg',
      alt: 'Artist collaboration',
      title: 'Artist Collaboration',
      category: 'Behind the Scenes'
    }
  ];

  const categories = ['All', 'Live Art', 'Music', 'Food & Drink', 'Atmosphere', 'Behind the Scenes', 'Artwork'];

  return (
    <div className="pt-16">
      {/* Header */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-7xl font-black text-white mb-6">
            Gallery
          </h1>
          <p className="text-xl text-gray-300 mb-8">
            Experience the magic through the lens of previous shows
          </p>
        </div>
      </section>

      {/* Category Filter */}
      <section className="px-4 sm:px-6 lg:px-8 mb-12">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category}
                className="px-4 py-2 rounded-lg border border-white/20 text-white hover:bg-white/10 transition-all font-medium"
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Grid */}
      <section className="px-4 sm:px-6 lg:px-8 pb-20">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {galleryImages.map((image) => (
              <div
                key={image.id}
                className="group relative overflow-hidden rounded-lg bg-gray-800 aspect-[4/3] hover:transform hover:scale-105 transition-all duration-300 cursor-pointer"
              >
                <Image
                  src={image.src}
                  alt={image.alt}
                  fill
                  className="object-cover group-hover:opacity-80 transition-opacity"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
                <div className="absolute bottom-4 left-4 right-4 transform translate-y-4 group-hover:translate-y-0 transition-transform">
                  <span className="inline-block bg-purple-600 text-white text-xs px-2 py-1 rounded-full mb-2">
                    {image.category}
                  </span>
                  <h3 className="text-white font-bold text-lg">{image.title}</h3>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/30 text-center">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold text-white mb-6">
            Be Part of the Next Show
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Join us for an unforgettable night of art, music, and chocolate
          </p>
          <a
            href="/tickets"
            className="inline-block bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-lg font-bold text-lg hover:from-blue-600 hover:to-purple-700 transition-all"
          >
            Get Your Tickets
          </a>
        </div>
      </section>
    </div>
  );
};

export default GalleryPage;
