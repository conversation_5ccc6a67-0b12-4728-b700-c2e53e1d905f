'use client';

import { useEffect, useRef } from 'react';
import Link from 'next/link';

export default function KusamaPlayground() {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Resize canvas to full window
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    if (prefersReducedMotion) {
      // Static dots for reduced motion
      const staticDots = Array.from({ length: 50 }, () => ({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        r: 2 + Math.random() * 8,
        hue: Math.random() * 360
      }));

      ctx.clearRect(0, 0, canvas.width, canvas.height);
      staticDots.forEach(dot => {
        ctx.beginPath();
        ctx.arc(dot.x, dot.y, dot.r, 0, Math.PI * 2);
        ctx.fillStyle = `hsl(${dot.hue}, 80%, 60%)`;
        ctx.fill();
      });

      return () => {
        window.removeEventListener('resize', resizeCanvas);
      };
    }

    // Animated dots for normal motion
    const dots = Array.from({ length: 180 }, () => ({
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      r: 2 + Math.random() * 5,
      dx: (Math.random() - 0.5) * 0.6,
      dy: (Math.random() - 0.5) * 0.6,
      hue: Math.random() * 360
    }));

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      dots.forEach(dot => {
        // Update position
        dot.x += dot.dx;
        dot.y += dot.dy;
        
        // Bounce off edges
        if (dot.x < 0 || dot.x > canvas.width) dot.dx *= -1;
        if (dot.y < 0 || dot.y > canvas.height) dot.dy *= -1;
        
        // Keep dots in bounds
        dot.x = Math.max(0, Math.min(canvas.width, dot.x));
        dot.y = Math.max(0, Math.min(canvas.height, dot.y));
        
        // Draw dot
        ctx.beginPath();
        ctx.arc(dot.x, dot.y, dot.r, 0, Math.PI * 2);
        ctx.fillStyle = `hsl(${(dot.x + dot.y) % 360}, 80%, 60%)`;
        ctx.fill();
      });
      
      requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
    };
  }, []);

  return (
    <div className="relative min-h-screen bg-black overflow-hidden">
      {/* Canvas */}
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full"
        style={{ display: 'block' }}
      />
      
      {/* Overlay Content */}
      <div className="relative z-10 p-8">
        <Link
          href="/artists"
          className="inline-flex items-center text-white/80 hover:text-white font-semibold transition-colors"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back to Artists
        </Link>
      </div>

      {/* Title */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-black text-white/90 mb-4">
            Kusama Playground
          </h1>
          <p className="text-xl text-white/70">
            Inspired by Yayoi Kusama's infinite dots
          </p>
        </div>
      </div>

      {/* Instructions */}
      <div className="absolute bottom-8 left-8 right-8 text-center">
        <p className="text-white/60 text-sm">
          Watch the dots dance • Resize your window to see them adapt
        </p>
      </div>
    </div>
  );
}
