export const metadata = {
  title: 'Contact — Chocolate & Art Show Dallas',
  description: 'Get in touch with the Chocolate & Art Show team. Contact us for questions, partnerships, or applications.',
};

const ContactPage = () => {
  return (
    <div className="pt-16">
      {/* Header */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-7xl font-black text-white mb-6">
            Contact
          </h1>
          <p className="text-xl text-gray-300 mb-8">
            Get in touch with the Chocolate & Art Show team
          </p>
        </div>
      </section>

      {/* Contact Information */}
      <section className="px-4 sm:px-6 lg:px-8 pb-20">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            
            {/* Contact Details */}
            <div className="space-y-8">
              <div>
                <h2 className="text-3xl font-bold text-white mb-6">Get In Touch</h2>
                <div className="space-y-6">
                  
                  {/* General Contact */}
                  <div className="bg-white/5 border border-white/10 rounded-lg p-6">
                    <h3 className="text-xl font-bold text-white mb-3">General Inquiries</h3>
                    <p className="text-gray-300 mb-4">
                      Questions about the event, tickets, or general information
                    </p>
                    <a 
                      href="mailto:<EMAIL>"
                      className="text-blue-400 hover:text-blue-300 font-semibold"
                    >
                      <EMAIL>
                    </a>
                  </div>

                  {/* Artist Applications */}
                  <div className="bg-white/5 border border-white/10 rounded-lg p-6">
                    <h3 className="text-xl font-bold text-white mb-3">Artist Applications</h3>
                    <p className="text-gray-300 mb-4">
                      Apply to showcase your art at our next event
                    </p>
                    <a 
                      href="mailto:<EMAIL>?subject=Artist%20Application%20—%20Dallas"
                      className="text-purple-400 hover:text-purple-300 font-semibold"
                    >
                      Apply as Artist
                    </a>
                  </div>

                  {/* Vendor/Musician Applications */}
                  <div className="bg-white/5 border border-white/10 rounded-lg p-6">
                    <h3 className="text-xl font-bold text-white mb-3">Vendors & Musicians</h3>
                    <p className="text-gray-300 mb-4">
                      Join us as a vendor or musician
                    </p>
                    <div className="space-y-2">
                      <div>
                        <a 
                          href="mailto:<EMAIL>?subject=Vendor%20Application%20—%20Dallas"
                          className="text-green-400 hover:text-green-300 font-semibold"
                        >
                          Apply as Vendor
                        </a>
                      </div>
                      <div>
                        <a 
                          href="mailto:<EMAIL>?subject=Musician%20Application%20—%20Dallas"
                          className="text-yellow-400 hover:text-yellow-300 font-semibold"
                        >
                          Apply as Musician
                        </a>
                      </div>
                    </div>
                  </div>

                  {/* Press & Media */}
                  <div className="bg-white/5 border border-white/10 rounded-lg p-6">
                    <h3 className="text-xl font-bold text-white mb-3">Press & Media</h3>
                    <p className="text-gray-300 mb-4">
                      Media inquiries and press credentials
                    </p>
                    <a 
                      href="mailto:<EMAIL>?subject=Press%20Inquiry%20—%20Dallas"
                      className="text-red-400 hover:text-red-300 font-semibold"
                    >
                      Press Inquiries
                    </a>
                  </div>

                </div>
              </div>
            </div>

            {/* Event Location & Details */}
            <div className="space-y-8">
              <div>
                <h2 className="text-3xl font-bold text-white mb-6">Event Location</h2>
                
                {/* Venue Info */}
                <div className="bg-white/5 border border-white/10 rounded-lg p-6 mb-6">
                  <h3 className="text-xl font-bold text-white mb-3">Lofty Spaces</h3>
                  <div className="text-gray-300 space-y-2">
                    <p>Dallas, TX</p>
                    <p>Exact address provided with ticket confirmation</p>
                  </div>
                </div>

                {/* Event Details */}
                <div className="bg-white/5 border border-white/10 rounded-lg p-6 mb-6">
                  <h3 className="text-xl font-bold text-white mb-3">Event Details</h3>
                  <div className="text-gray-300 space-y-2">
                    <p><strong>Dates:</strong> September 18-19, 2025</p>
                    <p><strong>Time:</strong> Doors at 7:00 PM</p>
                    <p><strong>Age:</strong> 21+ (ID Required)</p>
                    <p><strong>Dress Code:</strong> Creative Casual</p>
                  </div>
                </div>

                {/* Transportation */}
                <div className="bg-white/5 border border-white/10 rounded-lg p-6">
                  <h3 className="text-xl font-bold text-white mb-3">Getting There</h3>
                  <div className="text-gray-300 space-y-2">
                    <p>Rideshare recommended (Uber/Lyft)</p>
                    <p>Limited parking available</p>
                    <p>Public transportation options nearby</p>
                  </div>
                </div>

              </div>
            </div>

          </div>
        </div>
      </section>

      {/* Social Media */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/30 text-center">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold text-white mb-6">
            Follow Us
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Stay updated on the latest news and behind-the-scenes content
          </p>
          <div className="flex justify-center space-x-6">
            <a href="#" className="text-gray-400 hover:text-white transition-colors">
              <span className="sr-only">Instagram</span>
              <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.864 3.708 13.713 3.708 12.416s.49-2.448 1.418-3.323c.875-.875 2.026-1.297 3.323-1.297s2.448.422 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.275c-.875.807-2.026 1.297-3.323 1.297z"/>
              </svg>
            </a>
            <a href="#" className="text-gray-400 hover:text-white transition-colors">
              <span className="sr-only">Facebook</span>
              <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            </a>
            <a href="#" className="text-gray-400 hover:text-white transition-colors">
              <span className="sr-only">Twitter</span>
              <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
              </svg>
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage;
