import type { Metadata } from "next";
import { Inter, Prompt } from "next/font/google";
import "./globals.css";
import Navigation from "@/components/Navigation";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const prompt = Prompt({
  weight: ["400", "600", "800"],
  subsets: ["latin"],
  variable: "--font-prompt",
});

export const metadata: Metadata = {
  title: "Chocolate & Art Show — Dallas",
  description: "Immersive art, live music, body painting, and artisan chocolate. Two nights only in Dallas. 21+.",
  keywords: ["art show", "chocolate", "music", "Dallas", "live art", "body painting", "nightlife"],
  openGraph: {
    title: "Chocolate & Art Show — Dallas",
    description: "Immersive art, live music, body painting, and artisan chocolate. Two nights only in Dallas. 21+.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Chocolate & Art Show — Dallas",
    description: "Immersive art, live music, body painting, and artisan chocolate. Two nights only in Dallas. 21+.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className={`${inter.variable} ${prompt.variable} antialiased`}>
        <Navigation />
        <main>{children}</main>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Event",
              name: "Chocolate and Art Show — Dallas",
              startDate: "2025-09-18T19:00:00-05:00",
              endDate: "2025-09-19T23:59:00-05:00",
              eventAttendanceMode: "https://schema.org/OfflineEventAttendanceMode",
              eventStatus: "https://schema.org/EventScheduled",
              location: {
                "@type": "Place",
                name: "Lofty Spaces",
                address: {
                  "@type": "PostalAddress",
                  addressLocality: "Dallas",
                  addressRegion: "TX",
                  addressCountry: "US",
                },
              },
              description: "Immersive art, live music, body painting, and artisan chocolate. 21+.",
              offers: [
                {
                  "@type": "Offer",
                  url: "https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089",
                  priceCurrency: "USD",
                  availability: "https://schema.org/InStock",
                  validFrom: "2025-08-01T08:00:00-05:00",
                },
              ],
              performer: { "@type": "PerformingGroup", name: "Local Artists & DJs" },
              organizer: {
                "@type": "Organization",
                name: "Chocolate and Art Show",
                url: "https://chocolateandartshow.com",
              },
            }),
          }}
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Randomize artists header background on each refresh
              if (typeof window !== 'undefined') {
                document.documentElement.style.setProperty('--h', Math.floor(Math.random() * 360));
                document.documentElement.style.setProperty('--a', Math.floor(Math.random() * 360) + 'deg');
              }
            `,
          }}
        />
      </body>
    </html>
  );
}
