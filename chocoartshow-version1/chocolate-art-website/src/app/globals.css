@import "tailwindcss";

/* Global CSS Variables and Noise Background */
:root {
  --noise: url("data:image/svg+xml,%3Csvg viewBox='0 0 250 250' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='6.29' numOctaves='6' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

html,
body {
  height: 100%;
}

body {
  background: #0b0b0d;
  margin: 0;
  position: relative;
  color: #ececec;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

body::after {
  content: "";
  position: fixed;
  inset: 0;
  pointer-events: none;
  background: var(--noise);
  opacity: 0.38;
  mix-blend-mode: overlay;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 800;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

p {
  font-size: 1rem;
  line-height: 1.55;
}

@media (min-width: 768px) {
  p {
    font-size: 1.125rem;
  }
}

/* Focus styles for accessibility */
*:focus-visible {
  outline: 3px solid rgba(255, 255, 255, 0.3);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Waterfall/Perspective Headline */
.waterfall {
  transform-origin: left top;
  transform: perspective(720px) rotateX(55deg) rotateZ(-28deg) skewX(-8deg);
}

.waterfall .line {
  display: block;
  font-weight: 900;
  text-transform: uppercase;
  font-size: clamp(2rem, 14vw, 8rem);
  line-height: 0.9;
  color: #fff;
  text-shadow: 1px 1px 0 #fff, 2px 2px 0 #fff, 3px 3px 0 #fff, 4px 4px 0 #fff, 5px 5px 0 #fff, 6px 6px 0 #fff, 7px 7px 0 #fff, 8px 8px 0 #fff, 9px 9px 0 #fff, 10px 10px 0 #fff, 11px 11px 0 #fff, 12px 12px 0 #fff;
}

/* Scroll-linked cascade (progressive enhancement) */
@keyframes drift {
  to {
    transform: translateY(var(--y, 0));
  }
}

@supports (animation-timeline: scroll(root block)) {
  .waterfall .line {
    animation: drift linear both;
    animation-timeline: scroll(root block);
    animation-range: entry 10% cover 80%;
  }

  .waterfall .line:nth-child(1) {
    --y: 6vh;
  }

  .waterfall .line:nth-child(2) {
    --y: 12vh;
  }

  .waterfall .line:nth-child(3) {
    --y: 18vh;
  }
}

/* Gradient Text Shadow CTA */
.cta-gradient {
  font-family: "Prompt", system-ui, sans-serif;
  font-weight: 800;
  line-height: 0.9;
  margin: 0;
  background: linear-gradient(4deg, #548cff 10%, #f900bf 90%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  --shadow-size: 0.08em;
  padding: var(--shadow-size) 0 0 var(--shadow-size);
  text-shadow: calc(-1 * var(--shadow-size)) calc(-1 * var(--shadow-size)) #fff;
  font-size: clamp(2.6rem, 12vw, 7rem);
}

/* RGB Wall Interstitial */
.rgb-wall {
  isolation: isolate;
  min-height: 58vh;
  display: grid;
  place-items: center;
  gap: 2rem;
  padding: 8vh 4vw;
  background: #0f1114;
}

.rgb-hero {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: clamp(0.4rem, 2vw, 1.2rem);
}

.rgb-plus,
.rgb-eq {
  color: #8ea3b3;
  font-weight: 800;
  font-size: clamp(2rem, 8vw, 6rem);
}

.rgb-word {
  position: relative;
  display: inline-block;
  font-weight: 900;
  text-transform: uppercase;
  letter-spacing: -0.02em;
  line-height: 0.85;
  color: #eaeaea;
  font-size: clamp(3rem, 14vw, 12rem);
}

.rgb-word::before,
.rgb-word::after {
  content: attr(data-txt);
  position: absolute;
  inset: 0;
  z-index: -1;
}

.rgb-word::before {
  transform: translate(0.18em, 0.06em);
  color: #e53935;
  opacity: 0.8;
}

.rgb-word::after {
  transform: translate(-0.14em, 0.08em);
  color: #03a9f4;
  opacity: 0.75;
}

.rgb-cta {
  display: inline-block;
  font-weight: 900;
  letter-spacing: 0.04em;
  padding: 0.9rem 1.25rem;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.23);
  background: rgba(255, 255, 255, 0.07);
  color: #fff;
  text-decoration: none;
}

/* Artists Header with Trippy Background */
.artists-hero {
  position: relative;
  min-height: 48vh;
  border-radius: 20px;
  overflow: hidden;
  display: grid;
  place-items: end start;
  padding: 2.5rem;
  margin: 2rem;
  background: linear-gradient(rgba(0, 0, 0, 0.92), rgba(0, 0, 0, 0.92)),
    conic-gradient(from var(--a, 0deg),
      hsl(var(--h, 210) 90% 55%),
      hsl(calc(var(--h, 210) + 120) 90% 55%),
      hsl(calc(var(--h, 210) + 240) 90% 55%),
      hsl(var(--h, 210) 90% 55%));
}

.artists-hero::after {
  content: "";
  position: absolute;
  inset: 0;
  pointer-events: none;
  opacity: 0.25;
  mix-blend-mode: overlay;
  background: radial-gradient(1px 1px at 20% 10%, rgba(255, 255, 255, 0.05), transparent 40%),
    radial-gradient(1px 2px at 80% 30%, rgba(255, 255, 255, 0.04), transparent 45%);
}

.artists-hero h1 {
  margin: 0;
  font-weight: 900;
  font-size: clamp(2rem, 6vw, 4rem);
  color: #fff;
  position: relative;
  z-index: 10;
}

@media (prefers-reduced-motion: reduce) {
  .artists-hero {
    background: linear-gradient(rgba(0, 0, 0, 0.94), rgba(0, 0, 0, 0.94)), #0b0b0d;
  }
}

/* Kusama Card */
.kusama-card {
  display: grid;
  grid-template-columns: 140px 1fr;
  gap: 12px;
  align-items: center;
  padding: 12px;
  border: 1px dashed rgba(255, 255, 255, 0.2);
  border-radius: 14px;
  color: #e8eefc;
  text-decoration: none;
  transition: all 0.3s ease;
}

.kusama-card:hover {
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.05);
}

.kusama-card figure {
  margin: 0;
  border-radius: 10px;
  overflow: hidden;
}

.kusama-card span {
  font-weight: 800;
}

/* Artist Grid Styles */
.artist-preview {
  --card-bg: #101113;
  --card-bd: rgba(255, 255, 255, 0.1);
  --name: #f2f2f2;
}

.artist-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 28px;
  margin: 0;
  padding: 0;
  list-style: none;
}

.artist-link {
  display: grid;
  gap: 0.55rem;
  text-decoration: none;
  color: var(--name);
  outline: 0;
}

.artist-link:focus-visible {
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
  border-radius: 14px;
}

.media {
  background: linear-gradient(#15161a, #15161a) padding-box;
  border: 1px solid var(--card-bd);
  border-radius: 16px;
  overflow: hidden;
  aspect-ratio: 16 / 10;
}

.media img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  filter: saturate(0.98);
}

/* Organic masks mapped to the SVG clipPaths */
.blob-1 {
  clip-path: url(#blob-1);
}

.blob-2 {
  clip-path: url(#blob-2);
}

.blob-3 {
  clip-path: url(#blob-3);
}

.blob-4 {
  clip-path: url(#blob-4);
}

.blob-5 {
  clip-path: url(#blob-5);
}

/* Motion & hover */
.artist-card {
  transition: transform 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
}

.artist-card:hover {
  transform: translateY(-4px);
}

.name {
  font-weight: 800;
  letter-spacing: 0.02em;
  font-size: 1rem;
}

/* Fallback when clip-path unsupported */
@supports not (clip-path: url(#blob-1)) {
  .media {
    clip-path: none;
    border-radius: 18px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .artist-card {
    transition: none;
  }
}

/* Mobile density */
@media (max-width: 480px) {
  .artist-grid {
    gap: 18px;
  }
}

/* Visually hidden utility */
.visually-hidden {
  position: absolute !important;
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  width: 1px;
  height: 1px;
  overflow: hidden;
  white-space: nowrap;
}

/* Apply CTA Styles */
.apply-cta {
  --bd: rgba(255, 255, 255, 0.13);
  --fg: #e9edf3;
  background: #0e0f12;
  border: 1px solid var(--bd);
  border-radius: 18px;
  padding: 20px;
}

.apply-cta h2 {
  margin: 0.1rem 0 0.35rem;
  font-size: clamp(1.1rem, 3.2vw, 1.6rem);
}

.apply-cta p {
  margin: 0 0 0.8rem;
  color: #b8c0cc;
}

.apply-cta .microcopy {
  margin-top: 1rem;
  font-size: 0.875rem;
  color: #9ca3af;
  font-style: italic;
}

.apply-cta .btns {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.apply-cta .btn {
  display: inline-block;
  padding: 0.7rem 1rem;
  border-radius: 12px;
  background: #1d66ff;
  color: #fff;
  text-decoration: none;
  font-weight: 800;
  transition: all 0.3s ease;
}

.apply-cta .btn:hover {
  background: #1557e6;
  transform: translateY(-1px);
}

.apply-cta .btn:focus-visible {
  outline: 3px solid rgba(255, 255, 255, 0.2);
}

@media (max-width: 520px) {
  .apply-cta .btn {
    flex: 1 1 auto;
    text-align: center;
  }
}

/* CTA Box Styles */
.cta-box {
  --c1: #7f5af0;
  --c2: #2cb67d;
  --bg: #0b0b0d;
  display: grid;
  gap: 0.25rem;
  justify-items: center;
  text-align: center;
  padding: 1rem 1.25rem;
  border-radius: 14px;
  color: #fff;
  text-decoration: none;
  font-weight: 800;
  background: linear-gradient(rgba(255, 255, 255, 0.06), rgba(255, 255, 255, 0.06)) padding-box,
    conic-gradient(from var(--ang, 0deg), var(--c1), var(--c2), var(--c1)) border-box;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.cta-box:hover {
  --ang: 180deg;
  transform: translateY(-2px);
}

.cta-title {
  font-size: clamp(1.2rem, 4.2vw, 2rem);
  letter-spacing: 0.02em;
}

.cta-sub {
  font-weight: 600;
  opacity: 0.85;
  font-size: 0.95rem;
}

@media (prefers-reduced-motion: reduce) {
  .cta-box {
    background: linear-gradient(rgba(255, 255, 255, 0.06), rgba(255, 255, 255, 0.06)) padding-box,
      rgba(107, 107, 107, 0.2) border-box;
  }

  .cta-box:hover {
    transform: none;
  }
}

/* Plaque Styles */
.plaque {
  --b1: #dcdcdc;
  --b2: #8f8f8f;
  background: #7d7d7d;
  color: #fff;
  padding: 0.6em 0.9em;
  border-radius: 10px;
  font-style: italic;
  font-weight: 700;
  font-size: clamp(1.4rem, 4.8vw, 2.6rem);
  border: 8px solid var(--b1);
  box-shadow: inset 0 0 0 8px var(--b2);
  display: inline-block;
}

/* Night Selector Styles */
.night-select {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.night {
  appearance: none;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: #121419;
  color: #eaf0ff;
  border-radius: 12px;
  padding: 0.85rem 1.1rem;
  font-weight: 900;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.6rem;
}

.night:hover {
  background: #1a1f26;
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.night:focus-visible {
  outline: 3px solid rgba(255, 255, 255, 0.3);
  outline-offset: 2px;
}

.night.selected {
  background: #2563eb;
  border-color: #3b82f6;
}

.night .tag {
  margin-left: 0.6rem;
  font-size: 0.75em;
  background: #ffd24a;
  color: #1a1200;
  padding: 0.2rem 0.45rem;
  border-radius: 0.6rem;
  font-weight: 900;
}

.night[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.night[disabled]:hover {
  transform: none;
  background: #121419;
  border-color: rgba(255, 255, 255, 0.2);
}

/* Screen reader only utility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Artist Detail Page Styles */
.portrait {
  background: #15161a;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  aspect-ratio: 16/10;
  clip-path: url(#blob-2);
}

.portrait img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Carousel (scroll-snap) */
.carousel {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  padding-bottom: 8px;
}

.slide {
  flex: 0 0 82%;
  scroll-snap-align: center;
  background: #111319;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 14px;
  overflow: hidden;
}

.slide img {
  display: block;
  width: 100%;
  height: auto;
}

.slide figcaption {
  padding: 0.65rem 0.8rem;
  font-size: 0.95rem;
  color: #b6beca;
}

/* Fallback when clip-path unsupported for portrait */
@supports not (clip-path: url(#blob-2)) {
  .portrait {
    clip-path: none;
    border-radius: 18px;
  }
}

@media (max-width: 760px) {
  .portrait {
    margin-bottom: 2rem;
  }
}