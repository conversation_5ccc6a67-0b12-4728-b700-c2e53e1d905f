'use client';

import { useState } from 'react';

export default function FAQPage() {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const faqs = [
    {
      question: "What is the Chocolate & Art Show?",
      answer: "The Chocolate & Art Show is an immersive experience combining live art creation, artisan chocolate tastings, live music, and interactive experiences. Artists create works in real-time while guests enjoy premium chocolate and entertainment."
    },
    {
      question: "What's included with my ticket?",
      answer: "Your ticket includes access to all live art demonstrations, chocolate tastings, live music performances, interactive art experiences, and body painting demonstrations. A cash bar is available for additional beverages."
    },
    {
      question: "Is this event 21+ only?",
      answer: "Yes, this is a 21+ event. Valid government-issued photo ID is required for entry. No exceptions."
    },
    {
      question: "What time does the event start and end?",
      answer: "Doors open at 7:00 PM both nights. The event runs until late, with last entry at 12:30 AM. We recommend arriving early to experience everything the show has to offer."
    },
    {
      question: "Where is the event located?",
      answer: "The event takes place at Lofty Spaces in Dallas. The exact address and directions will be provided with your ticket confirmation."
    },
    {
      question: "Can I buy tickets at the door?",
      answer: "We strongly recommend purchasing tickets in advance as this is a limited capacity event. Door sales are not guaranteed and will only be available if the event is not sold out."
    },
    {
      question: "What should I wear?",
      answer: "The dress code is creative casual. Feel free to express yourself! Comfortable shoes are recommended as you'll be standing and walking around to view different art stations."
    },
    {
      question: "Can I take photos?",
      answer: "Yes! Photography is encouraged. We love seeing guests share their experience on social media. Please be respectful of artists while they're working and ask permission before photographing people."
    },
    {
      question: "Is food available?",
      answer: "Artisan chocolate tastings are included with your ticket. Light snacks may be available for purchase. Outside food and beverages are not permitted."
    },
    {
      question: "What if I have dietary restrictions?",
      answer: "Please contact <NAME_EMAIL> before the event if you have severe allergies or dietary restrictions. We'll do our best to accommodate your needs."
    },
    {
      question: "Can I get a refund?",
      answer: "All sales are final. However, if you cannot attend, you may transfer your ticket to someone else. Please contact us for assistance with ticket transfers."
    },
    {
      question: "How can I apply to be an artist or vendor?",
      answer: "We're always looking for talented artists, vendors, and musicians! Visit our Artists page for application information or email us <NAME_EMAIL>."
    }
  ];

  return (
    <div className="pt-16">
      {/* Header */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-7xl font-black text-white mb-6">
            FAQ
          </h1>
          <p className="text-xl text-gray-300 mb-8">
            Everything you need to know about the Chocolate & Art Show
          </p>
        </div>
      </section>

      {/* FAQ Items */}
      <section className="px-4 sm:px-6 lg:px-8 pb-20">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div 
                key={index}
                className="bg-white/5 border border-white/10 rounded-lg overflow-hidden"
              >
                <button
                  className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-white/5 transition-colors"
                  onClick={() => toggleItem(index)}
                  aria-expanded={openItems.includes(index)}
                >
                  <span className="text-white font-semibold text-lg pr-4">
                    {faq.question}
                  </span>
                  <svg 
                    className={`w-5 h-5 text-gray-400 transform transition-transform ${
                      openItems.includes(index) ? 'rotate-180' : ''
                    }`}
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                
                {openItems.includes(index) && (
                  <div className="px-6 pb-4">
                    <p className="text-gray-300 leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/30 text-center">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold text-white mb-6">
            Still Have Questions?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            We're here to help! Reach out to us directly.
          </p>
          <a
            href="mailto:<EMAIL>"
            className="inline-block bg-white/10 border border-white/30 text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-white/20 transition-all"
          >
            Contact Us
          </a>
        </div>
      </section>
    </div>
  );
}
