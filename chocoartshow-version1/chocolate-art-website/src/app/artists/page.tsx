import ArtistGrid from '@/components/ArtistGrid';
import ApplyCTA from '@/components/ApplyCTA';

export const metadata = {
  title: 'Artists — Chocolate & Art Show Dallas',
  description: 'Meet the talented local and emerging artists featured at the Chocolate & Art Show in Dallas.',
};

const ArtistsPage = () => {
  return (
    <div className="pt-16">
      {/* Artists Header with Trippy Background */}
      <header className="artists-hero">
        <h1>Artists</h1>
      </header>

      {/* Apply CTA */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <ApplyCTA />
      </div>

      {/* Artist Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <ArtistGrid />
      </div>

      {/* Kusama Playground Teaser */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <a 
          className="kusama-card" 
          href="/play/kusama" 
          aria-label="Open the Kusama dot playground"
        >
          <figure>
            <img 
              src="/api/placeholder/280/160" 
              alt="Preview of Kusama dot playground" 
              loading="lazy" 
              width="280" 
              height="160"
              className="w-full h-auto"
            />
          </figure>
          <span>Play with the dots →</span>
        </a>
      </div>
    </div>
  );
};

export default ArtistsPage;
