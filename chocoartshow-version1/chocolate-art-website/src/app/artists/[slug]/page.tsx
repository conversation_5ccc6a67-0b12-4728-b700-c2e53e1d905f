import Link from 'next/link';
import Image from 'next/image';
import { notFound } from 'next/navigation';

// Mock artist data - in a real app, this would come from a CMS or database
const artists = {
  'danica': {
    name: '<PERSON><PERSON>',
    medium: 'Mixed Media',
    location: 'Dallas',
    instagram: '@danica.art',
    bio: '<PERSON><PERSON> is a mixed media artist based in Dallas who specializes in combining traditional painting techniques with digital elements. Her work explores themes of urban life and human connection in the digital age.',
    image: '/assets/artists/danica/portrait.jpg',
    works: [
      {
        title: 'Urban Dreams',
        medium: 'Acrylic and digital, 2024',
        image: '/assets/artists/danica/work1.jpg'
      },
      {
        title: 'Connection',
        medium: 'Mixed media, 2024',
        image: '/assets/artists/danica/work2.jpg'
      },
      {
        title: 'Digital Souls',
        medium: 'Oil and digital, 2023',
        image: '/assets/artists/danica/work3.jpg'
      }
    ]
  },
  'david-v': {
    name: '<PERSON>',
    medium: 'Street Art',
    location: 'Dallas',
    instagram: '@davidv.art',
    bio: '<PERSON> brings the energy of street art into gallery spaces. His bold, colorful works draw inspiration from hip-hop culture and urban landscapes.',
    image: '/assets/artists/david-v/portrait.jpg',
    works: [
      {
        title: 'City Rhythms',
        medium: 'Spray paint on canvas, 2024',
        image: '/assets/artists/david-v/work1.jpg'
      },
      {
        title: 'Beat Drop',
        medium: 'Mixed media, 2024',
        image: '/assets/artists/david-v/work2.jpg'
      }
    ]
  },
  'omi': {
    name: 'Omi',
    medium: 'Digital Art',
    location: 'Dallas',
    instagram: '@omi.creates',
    bio: 'Omi creates stunning digital artworks that blur the line between reality and fantasy. Their work often features vibrant colors and surreal landscapes.',
    image: '/assets/artists/omi/portrait.jpg',
    works: [
      {
        title: 'Neon Dreams',
        medium: 'Digital art, 2024',
        image: '/assets/artists/omi/work1.jpg'
      },
      {
        title: 'Cyber Garden',
        medium: 'Digital art, 2024',
        image: '/assets/artists/omi/work2.jpg'
      }
    ]
  },
  'sofia': {
    name: 'Sofía',
    medium: 'Sculpture',
    location: 'Dallas',
    instagram: '@sofia.sculpts',
    bio: 'Sofía creates contemporary sculptures that explore themes of identity and transformation. Her work often incorporates found objects and unconventional materials.',
    image: '/assets/artists/sofia/portrait.jpg',
    works: [
      {
        title: 'Metamorphosis',
        medium: 'Bronze and steel, 2024',
        image: '/assets/artists/sofia/work1.jpg'
      },
      {
        title: 'Identity Fragments',
        medium: 'Mixed materials, 2023',
        image: '/assets/artists/sofia/work2.jpg'
      }
    ]
  },
  'leon': {
    name: 'León',
    medium: 'Photography',
    location: 'Dallas',
    instagram: '@leon.lens',
    bio: 'León captures the essence of Dallas through his lens, focusing on the intersection of architecture and human emotion in urban environments.',
    image: '/assets/artists/leon/portrait.jpg',
    works: [
      {
        title: 'Dallas After Dark',
        medium: 'Photography series, 2024',
        image: '/assets/artists/leon/work1.jpg'
      },
      {
        title: 'Urban Solitude',
        medium: 'Photography, 2024',
        image: '/assets/artists/leon/work2.jpg'
      }
    ]
  }
};

interface PageProps {
  params: Promise<{ slug: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { slug } = await params;
  const artist = artists[slug as keyof typeof artists];

  if (!artist) {
    return {
      title: 'Artist Not Found — Chocolate & Art Show Dallas',
    };
  }

  return {
    title: `${artist.name} — Chocolate & Art Show Dallas`,
    description: `Meet ${artist.name}, a ${artist.medium} artist featured at the Chocolate & Art Show in Dallas. ${artist.bio}`,
  };
}

export default async function ArtistPage({ params }: PageProps) {
  const { slug } = await params;
  const artist = artists[slug as keyof typeof artists];

  if (!artist) {
    notFound();
  }

  return (
    <div className="pt-16">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Back Link */}
        <Link
          href="/artists"
          className="inline-flex items-center text-blue-400 hover:text-blue-300 font-semibold mb-8 transition-colors"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back to Artists
        </Link>

        {/* Artist Header */}
        <header className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-end mb-12">
          <figure className="portrait">
            <Image
              src={artist.image}
              alt={`${artist.name} — portrait`}
              width={640}
              height={420}
              className="w-full h-auto"
            />
          </figure>
          <div>
            <h1 className="text-4xl md:text-6xl font-black text-white mb-2">
              {artist.name}
            </h1>
            <div className="text-gray-400 font-semibold mb-4">
              {artist.medium} • {artist.location} • {artist.instagram}
            </div>
            <p className="text-gray-300 text-lg leading-relaxed max-w-2xl">
              {artist.bio}
            </p>
          </div>
        </header>

        {/* Navigation Buttons */}
        <div className="flex gap-4 mb-8">
          <button
            type="button"
            onClick={() => {
              const carousel = document.querySelector('.carousel');
              if (carousel) {
                carousel.scrollBy({ left: -500, behavior: 'smooth' });
              }
            }}
            className="bg-white/10 border border-white/30 text-white px-4 py-2 rounded-lg font-bold hover:bg-white/20 transition-all"
          >
            ◀︎ Prev
          </button>
          <button
            type="button"
            onClick={() => {
              const carousel = document.querySelector('.carousel');
              if (carousel) {
                carousel.scrollBy({ left: 500, behavior: 'smooth' });
              }
            }}
            className="bg-white/10 border border-white/30 text-white px-4 py-2 rounded-lg font-bold hover:bg-white/20 transition-all"
          >
            Next ▶︎
          </button>
        </div>

        {/* Artwork Carousel */}
        <section aria-label={`Artwork by ${artist.name}`} className="carousel">
          {artist.works.map((work, index) => (
            <figure key={index} className="slide">
              <Image
                src={work.image}
                alt={`${work.title} by ${artist.name}`}
                width={800}
                height={600}
                loading="lazy"
                className="w-full h-auto"
              />
              <figcaption className="p-4 text-gray-300">
                <strong>{work.title}</strong> — {work.medium}
              </figcaption>
            </figure>
          ))}
        </section>

      </div>
    </div>
  );
}

// Generate static params for all artists
export async function generateStaticParams() {
  return Object.keys(artists).map((slug) => ({
    slug,
  }));
}
