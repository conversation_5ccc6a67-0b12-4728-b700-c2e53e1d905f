import CTABox from '@/components/CTABox';
import NightSelector from '@/components/NightSelector';
import PricingGrid from '@/components/PricingGrid';
import UrgencyStrip from '@/components/UrgencyStrip';

export const metadata = {
  title: 'Tickets — Chocolate & Art Show Dallas',
  description: 'Get your tickets for the Chocolate & Art Show in Dallas. Two nights only - September 18-19, 2025.',
};

const TicketsPage = () => {
  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="cta-gradient mb-6">Buy Dallas Tickets</h1>
          <p className="text-xl text-gray-300 mb-8">
            Two nights of immersive art, live music, and artisan chocolate
          </p>
          <CTABox />
        </div>
      </section>

      {/* Urgency Strip */}
      <UrgencyStrip />

      {/* Night Selector */}
      <section className="py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-8">
            Choose Your Night
          </h2>
          <NightSelector />
        </div>
      </section>

      {/* Pricing Grid */}
      <section className="py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            Ticket Options
          </h2>
          <PricingGrid />
        </div>
      </section>

      {/* Event Details */}
      <section className="py-12 px-4 sm:px-6 lg:px-8 bg-black/30">
        <div className="max-w-4xl mx-auto">
          <div className="plaque text-center mb-8">
            21+ • Last entry 12:30 AM • ID required
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-gray-300">
            <div>
              <h3 className="text-xl font-bold text-white mb-4">What's Included</h3>
              <ul className="space-y-2">
                <li>• Access to live art creation</li>
                <li>• Artisan chocolate tastings</li>
                <li>• Live music performances</li>
                <li>• Interactive art experiences</li>
                <li>• Body painting demonstrations</li>
                <li>• Cash bar available</li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-xl font-bold text-white mb-4">Important Info</h3>
              <ul className="space-y-2">
                <li>• Doors open at 7:00 PM</li>
                <li>• Last entry at 12:30 AM</li>
                <li>• Valid ID required (21+)</li>
                <li>• No outside food or drinks</li>
                <li>• Photography encouraged</li>
                <li>• Dress code: Creative casual</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold text-white mb-6">
            Don't Miss Out
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Limited capacity • Almost sold out • Two nights only
          </p>
          <CTABox />
        </div>
      </section>
    </div>
  );
};

export default TicketsPage;
